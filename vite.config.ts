import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // Set base path based on environment and command
  let basePath = '/'

  if (mode === 'production') {
    // For S3 deployment
    basePath = '/trainer-guru/bril/myactivity/'
  } else if (command === 'build') {
    // For local testing of build (relative paths)
    basePath = './'
  }

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@components': path.resolve(__dirname, './src/components'),
        '@screens': path.resolve(__dirname, './src/screens'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@api': path.resolve(__dirname, './src/api'),
        '@types': path.resolve(__dirname, './src/types'),
        '@custom-types': path.resolve(__dirname, './src/types/custom-types'),
        '@assets': path.resolve(__dirname, './src/assets'),
        '@styles': path.resolve(__dirname, './src/styles'),
        '@store': path.resolve(__dirname, './src/store'),
        '@utils': path.resolve(__dirname, './src/utils')
      }
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: false,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            redux: ['@reduxjs/toolkit', 'react-redux', 'redux-saga'],
            ui: ['styled-components', 'lottie-react']
          }
        }
      }
    },
    base: basePath,
  server: {
    port: 3000,
    host: true,
    proxy: {
      // Remove '/ecoach-api' and '/ecoach-api/configuration' proxies as the prefix is no longer needed
    }
  },
    preview: {
      port: 3000,
      host: true
    }
  }
})
