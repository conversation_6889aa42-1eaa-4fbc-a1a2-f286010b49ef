// import the original type declarations
import 'i18next';
// import all namespaces (for the default language, only)
import commonEn from '../i18n/locales/en/common';
import ecoachEn from '../i18n/locales/en/ecoach';

declare module 'i18next' {
  // Extend CustomTypeOptions
  interface CustomTypeOptions {
    // custom namespace type, if you changed it
    defaultNS: 'common';
    // custom resources type
    resources: {
      common: typeof commonEn;
      ecoach: typeof ecoachEn;
    };
    // other
    returnNull: false;
  }
}