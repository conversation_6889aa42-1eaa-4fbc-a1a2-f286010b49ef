import { useCallback, useEffect, useState } from "react";
import {
  ConversationData,
  ConversationHistoryResponse,
  ConversationType,
} from "../@custom-types/ecoach";
import { getConversationHistoryByPaging } from "../api/ecoach";
import { useActionApi } from "@hooks/useActionApi";

const HOMEPAGE_ITEM_NEED = 4;

export type ConversationHistoryInput = {
  limit?: number;
  pageParam?: string;
  conversationType?: ConversationType;
};

export function useReportHistoryLatest(
  itemNeeded = HOMEPAGE_ITEM_NEED,
  conversationType?: ConversationType
) {
  const [allConversation, setAllConversation] = useState<ConversationData[]>(
    []
  );
  const [fetchCount, setFetchCount] = useState<number>(1);
  const [maxFetchCount, setMaxFetchCount] = useState<number>(10);
  const [loading, setLoading] = useState<boolean>(false);
  const [lastEvaluatedKey, setLastEvaluatedKey] = useState<string>("");
  const [itemNeed, setItemNeed] = useState<number>(itemNeeded);
  const [hasMore, setHasMore] = useState<boolean>(true);

  const actionGetConversationHistory = useActionApi(
    getConversationHistoryByPaging
  );

  const fetchConversations = useCallback(
    async (limit: number, conversationType?: ConversationType) => {
      if (
        loading ||
        allConversation.length >= itemNeed ||
        fetchCount > maxFetchCount ||
        !hasMore
      ) {
        return;
      }
      setLoading(true);
      try {
        const response = await actionGetConversationHistory.execute({
          body: { limit, pageParam: lastEvaluatedKey, conversationType },
          loading: { type: "local", name: "getHistoryDataLoading" },
        });
        // console.log("fetchConversations response.data", response.data);
        if (response.data) {
          const { items, has_more, last_evaluated_key, total_count } =
            response.data;
          const readyItems = items
            // .filter((item) => item.report_is_ready === "true")
            .map((item, index) => ({
              ...item,
              session_number: total_count - index - allConversation.length,
            }));

          setAllConversation((prev) => [...prev, ...readyItems]);
          setHasMore(has_more);
          setLastEvaluatedKey(last_evaluated_key);
          setFetchCount((prev) => prev + 1);
        }
      } catch (error) {
        setFetchCount((prev) => prev + 1);
        console.error("Failed to fetch conversation history:", error);
      } finally {
        setLoading(false);
      }
    },
    [
      loading,
      itemNeed,
      fetchCount,
      maxFetchCount,
      lastEvaluatedKey,
      hasMore,
      actionGetConversationHistory,
    ]
  );

  useEffect(() => {
    if (itemNeeded > 0) {
      fetchConversations(itemNeeded === HOMEPAGE_ITEM_NEED ? 5 : 100, conversationType);
    }
  }, [fetchConversations, itemNeeded, conversationType]);

  const fetchNextPage = useCallback(
    (itemNeeded: number) => {
      if (hasMore && !loading) {
        setItemNeed(itemNeeded);
        setMaxFetchCount(10);
        fetchConversations(100);
      }
    },
    [hasMore, loading, fetchConversations]
  );

  return {
    data: allConversation,
    loading,
    hasMore,
    fetchNextPage,
  };
}
