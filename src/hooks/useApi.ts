import { useCallback, useEffect, useState } from "react";
import axios from "axios";

interface ApiResponse<T> {
  data?: T;
  loading?: boolean;
  error?: any;
}

const useApi = <T>(api: (body?: any, cancelToken?: any, onUploadProgress?: any) => Promise<{ data: T }>) => {
  const [response, setResponse] = useState<ApiResponse<T>>({});
  const [cancelToken, setCancelToken] = useState(axios.CancelToken.source());

  useEffect(() => {
    return () => cancelToken.cancel();
  }, [cancelToken]);

  const action = useCallback(
    async (body?: any, successResponse?: any, onUploadProgress?: any) => {
      let newCancelToken = axios.CancelToken.source();
      setCancelToken(newCancelToken);
      setResponse({ loading: true });
      try {
        const { data } = await api(body, newCancelToken.token, onUploadProgress);
        setResponse({ data, ...successResponse });
      } catch (e) {
        console.error(e);
        setResponse({ error: e });
      }
    },
    [api]
  );

  return [response, action] as const;
};

export default useApi;
