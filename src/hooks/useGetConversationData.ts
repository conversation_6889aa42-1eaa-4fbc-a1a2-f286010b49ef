import { useState, useEffect } from 'react';
import { ConversationData, ConversationType } from '../types/ecoach-custom';
import { getConversationData } from '../api/ecoach';

interface UseGetConversationDataReturn {
  data: ConversationData | null;
  loading: boolean;
  error: string | null;
}

export const useGetConversationData = (conversationId: string): UseGetConversationDataReturn => {
  const [data, setData] = useState<ConversationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!conversationId) return;

    const fetchConversationData = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getConversationData({ conversationId });
        setData({
          ...response.data,
          conversation_type: response.data.conversation_type as ConversationType | undefined,
        });
      } catch (err: any) {
        setError(err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchConversationData();
  }, [conversationId]);

  return { data, loading, error };
};
