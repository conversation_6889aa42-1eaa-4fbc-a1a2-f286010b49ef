import { useSearchParams } from "react-router-dom";
import { useLocalStorage } from "usehooks-ts";

const ACCESS_TOKEN_KEY = "_ma_accessToken";
const USER_NAME_KEY = "_ma_userName";
const ACCESS_TOKEN_SEARCH_PARAM = "AccessTokenTrainerGuru";
const USER_NAME_SEARCH_PARAM = "AgentName";

declare global {
  interface Window {
    MyActivityController?: {
      postMessage: (message: string) => void;
    };
    MSStream?: any;
  }
}

const AppController = {
  postMessage: (message: string) => {
    if (window.MyActivityController) {
      window.MyActivityController.postMessage(message);
    } else {
      console.warn("MyActivityController is not available");
    }
  },
};

function quitWebView() {
  AppController.postMessage("quit");
}

export default function useMyActivity() {
  const [searchParams] = useSearchParams();
  const [accessToken] = useLocalStorage(
    ACCESS_TOKEN_KEY,
    () =>
      (window as any)[ACCESS_TOKEN_KEY] ||
      searchParams.get(ACCESS_TOKEN_SEARCH_PARAM) ||
      import.meta.env.VITE_DEFAULT_ACCESS_TOKEN ||
      null
  );
  const [userName] = useLocalStorage(
    USER_NAME_KEY,
    () => (window as any)[USER_NAME_KEY] || searchParams.get(USER_NAME_SEARCH_PARAM) || "Bancass Empat"
  );

  const AppController = window.MyActivityController;
  const isWebView = !!AppController;
  const isIOS =
    /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
  const isAndroid = /Android/.test(navigator.userAgent);

  return {
    isWebView,
    isIOS,
    isAndroid,
    accessToken,
    userName,
    quitWebView,
  };
}
