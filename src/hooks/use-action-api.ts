import { useCallback } from 'react';

export interface UseActionApiOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

const useActionApi = (apiFunction: (...args: any[]) => Promise<any>, options?: UseActionApiOptions) => {
  const execute = useCallback(async (...args: any[]) => {
    try {
      const result = await apiFunction(...args);
      options?.onSuccess?.(result);
      return result;
    } catch (error) {
      options?.onError?.(error);
      throw error;
    }
  }, [apiFunction, options]);

  return { execute };
};

export default useActionApi;
