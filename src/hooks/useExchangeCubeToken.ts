import { useEffect, useState } from "react";
import { useAppDispatch } from "../store/index";
import { setToken } from "../store/ecoachSlice";
import { getVerifyCubeToken } from "../api/ecoach";
import { useActionApi } from "./useActionApi";
import useMyActivity from "./use-my-activity";

export const useExchangeCubeToken = () => {
  const dispatch = useAppDispatch();
  const [cubeToken, setCubeToken] = useState("");
  const { accessToken } = useMyActivity();

  const actionVerifyCubeToken = useActionApi(getVerifyCubeToken);

  useEffect(() => {
    dispatch(setToken(accessToken));
    setCubeToken(accessToken);

    // Comment out the actual API call for now
    // actionVerifyCubeToken.execute().then((response) => {
    //   const { data } = response;
    //   dispatch(setToken(data.jwt_token));
    //   setCubeToken(data.jwt_token);
    // }).catch((error) => {
    //   console.error("Failed to exchange cube token:", error);
    // });
  }, []);

  return { cubeToken };
};
