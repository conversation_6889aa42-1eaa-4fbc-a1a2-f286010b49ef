import { useState, useEffect } from 'react';

// Tablet breakpoints (similar to common responsive design patterns)
const TABLET_MIN_WIDTH = 768; // iPad and similar tablets
// const TABLET_MAX_WIDTH = 1024; // Large tablets
const MOBILE_MAX_WIDTH = 767; // Mobile devices

interface LayoutAdoptionCheck {
  isTabletMode: boolean;
  isMobileMode: boolean;
  // isDesktopMode: boolean;
}

export default function useLayoutAdoptionCheck(): LayoutAdoptionCheck {
  const [dimensions, setDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    const handleResize = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Determine device type based on screen width
  // const isTabletMode = dimensions.width >= TABLET_MIN_WIDTH && dimensions.width <= TABLET_MAX_WIDTH;
  const isTabletMode = dimensions.width >= TABLET_MIN_WIDTH;
  const isMobileMode = dimensions.width <= MOBILE_MAX_WIDTH;
  // const isDesktopMode = dimensions.width > TABLET_MAX_WIDTH;

  return {
    isTabletMode,
    isMobileMode,
    // isDesktopMode,
  };
}
