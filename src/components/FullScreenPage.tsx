import Portal from "./portal";
import ScrollBar from "./scroll-bar";
import styled from "styled-components";
import { LeftHeader, TitleHeader } from "./modal-full-page/styled";
import { ButtonIcon } from "@styles/buttons";
import Icons from "./icons";
import { Spacing } from "../styles";
import { colors } from "./CubeBaseElement";

interface ModalFullPageProps {
  children: JSX.Element | JSX.Element[];
  showHeader?: boolean;
  title?: any;
  goBack?: () => void;
  backgroundImage?: string;
}

const ModalFullPageWrapper = styled.div<{ $showFooter?: boolean; $backgroundImage?: string }>`
  width: 100vw;
  height: 100vh;
  padding-bottom: ${({ $showFooter }) => ($showFooter ? "84px" : "")};
  position: fixed;
  top: 0;
  left: 0;
  z-index: -2;
  overflow: auto;
  background: #e5e5e5;

  :after {
    content: "";
    position: absolute;
    top: -10%;
    right: 0;
    bottom: 50%;
    left: 0;
    z-index: -2;

    /* background: #e5e5e5; */
    background-image: url(${(props) => props.$backgroundImage});
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }
`;

const Header = styled.div`
  width: 100%;
  padding: 15px 18px;
  position: fixed;
  top: 0;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: transparent;
`;

const FullScreenPage = ({ children, showHeader, title, goBack, backgroundImage }: ModalFullPageProps) => {
  return (
    <Portal>
      <ModalFullPageWrapper $showFooter={false} $backgroundImage={backgroundImage}>
        {showHeader && (
          <Header>
            <LeftHeader>
              {goBack && (
                <ButtonIcon onClick={goBack}>
                  <svg width="24" height="24" viewBox="0 0 24 24" fill={colors.white}>
                    <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                  </svg>
                </ButtonIcon>
              )}
              {title && (
                <TitleHeader>
                  <h6>{title}</h6>
                  <div style={{ flex: 1 }} />
                </TitleHeader>
              )}
            </LeftHeader>
          </Header>
        )}
        <ScrollBar>{children}</ScrollBar>
      </ModalFullPageWrapper>
    </Portal>
  );
};

export default FullScreenPage;
