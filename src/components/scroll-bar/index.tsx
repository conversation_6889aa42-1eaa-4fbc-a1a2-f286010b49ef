import React from 'react';
import styled from 'styled-components';

const ScrollContainer = styled.div`
  overflow-y: auto;
  overflow-x: hidden;
  
  /* Custom scrollbar styles */
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`;

interface ScrollBarProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const ScrollBar: React.FC<ScrollBarProps> = ({ children, className, style }) => {
  return (
    <ScrollContainer className={className} style={style}>
      {children}
    </ScrollContainer>
  );
};

export default ScrollBar;
