import React, { useEffect, useState } from "react";
import styled from "styled-components";
import { colors, H6, sizes } from "../CubeBaseElement";
import { loadingReport } from "../../assets";
import Modal from "./BaseModal";
import { useTranslation } from "react-i18next";

interface VideoCallLoadingModalProps {
  visible: boolean;
  avatarIsReady: boolean;
  onComplete: () => void;
}

const VideoCallLoadingModal: React.FC<VideoCallLoadingModalProps> = ({ 
  visible, 
  avatarIsReady, 
  onComplete 
}) => {
  const { t } = useTranslation("ecoach");
  const [currentBoldIndex, setCurrentBoldIndex] = useState(0);

  // Video call loading quote keys
  const quoteKeys = [
    'videoCallLoading1',
    'videoCallLoading2', 
    'videoCallLoading3',
    'videoCallLoading4',
    'videoCallLoading5'
  ];

  // Handle animation of bold styling every 2 seconds
  useEffect(() => {
    if (!visible) return;

    const interval = setInterval(() => {
      setCurrentBoldIndex((prev) => (prev + 1) % quoteKeys.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [visible]);

  // Close modal when avatar is ready
  useEffect(() => {
    if (avatarIsReady && visible) {
      onComplete();
    }
  }, [avatarIsReady, visible, onComplete]);

  if (!visible) {
    return null;
  }

  return (
    <Modal show={visible} size="full" title="">
      <Container>
        <LoadingGif src={loadingReport} alt="Loading" />
        <QuotesContainer>
          {quoteKeys.map((quoteKey, index) => (
            <QuoteWrapper key={index}>
              <QuoteText
                  fontWeight={index === currentBoldIndex ? "bold" : "normal"}
                  $isBold={index === currentBoldIndex}
              >
                {t(quoteKey as any)}
              </QuoteText>
            </QuoteWrapper>
          ))}
        </QuotesContainer>
      </Container>
    </Modal>
  );
};

const Container = styled.div`
  display: flex;
  flex: 1;
  width: 100%;
  height: 100vh;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background-color: "#FFFBF6";
`;

const LoadingGif = styled.img`
  width: 175px;
  height: 175px;
  margin-bottom: ${sizes[6]}px;
`;

const QuotesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[6]}px;
  width: 100%;
  align-items: center;
`;

const QuoteWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[2]}px;
  text-align: center;
  width: 100%;
`;

const QuoteText = styled(H6)<{ $isBold: boolean }>`
  color: ${colors.fwdDarkGreen[100]};
  transition: font-weight 0.3s ease;
  text-align: center;
  line-height: 1.4;
`;

export default VideoCallLoadingModal; 