import React, { useEffect, useMemo } from "react";
import { device } from "@styles/media";
import styled from "styled-components";
import { colors, H2, H6, sizes } from "../CubeBaseElement";
import Modal from "./BaseModal";
import { ButtonPrimary, ButtonSecondary } from "@styles/buttons";
import { TextAlign } from "../../types/custom-types";
import { missionCompleteBg, missionCompleteBgFailed } from "../../assets";

const BorderView = styled.div<{ notEnoughHeart: boolean; showButton: boolean }>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 500px;
  max-width: 420px;
  border-radius: ${sizes[4]}px;
  padding: 90px 20px 30px;
  padding-top: ${({ notEnoughHeart }) => (notEnoughHeart ? "20px" : "90px")};
  padding-bottom: ${({ notEnoughHeart }) => (notEnoughHeart ? "20px" : "30px")};
  background: ${({ notEnoughHeart }) =>
    `url(${notEnoughHeart ? missionCompleteBgFailed : missionCompleteBg}) no-repeat`};
  background-size: 100% 100%;
`;

const CenterView = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  max-width: 380px;
  border-radius: ${sizes[4]}px;
  padding: ${sizes[6]}px;

  //background-color: rgba(24, 48, 40, 0.75);
`;

const Container = styled.div`
  display: flex;
  flex: 1;
  border-radius: ${sizes[4]}px;
  overflow: hidden;
  justify-content: center;
  align-items: center;

  @media ${device.mobile} {
    margin-left: ${sizes[1]}px;
    margin-right: ${sizes[1]}px;
  }
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  overflow: hidden;
`;

const Spacer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
`;

type ModalProps = {
  title?: string;
  showButton?: boolean;
  primaryButtonText?: string;
  secondaryButtonText?: string;
  score?: number;
  missCompleteScore?: number;
  endCall?: () => void;
  onOkButton?: () => void;
  onExitRole?: () => void;
};

const MissionCompleteModal = ({
  title,
  showButton = false,
  primaryButtonText,
  secondaryButtonText,
  score,
  missCompleteScore,
  onOkButton,
  onExitRole,
  endCall,
}: ModalProps) => {
  const notEnoughHeart = useMemo(() => {
    return score !== undefined && missCompleteScore !== undefined && score < missCompleteScore;
  }, [score, missCompleteScore]);

  useEffect(() => {
    const timer = setTimeout(() => {
      !showButton && endCall?.();
    }, 10 * 1000);
    return () => clearTimeout(timer);
  }, [endCall, showButton]);

  return (
    <Modal show={true} size="ssm" title="" titleAlign={TextAlign.CENTER}>
      <Container>
        <BorderView notEnoughHeart={notEnoughHeart} showButton={showButton}>
          <CenterView>
            <Content>
              <H2 fontWeight="bold" color={colors.fwdGrey[20]} style={{ textAlign: "center", lineHeight: "60px" }}>
                {title || "Session completed"}
              </H2>
              <Spacer height={sizes[8]} />
              <H6
                fontWeight={"normal"}
                color={colors.fwdGrey[20]}
                style={{ textAlign: "center", paddingLeft: `${sizes[4]}px`, paddingRight: `${sizes[4]}px` }}
              >
                {notEnoughHeart
                  ? "You didn't get any heart today, but you can always try again. You can do it!"
                  : `Congratulations,\nyou got ${score} hearts.\n\nTry our role-play mode and sell a product in 15 minutes!`}
              </H6>
              {showButton && (
                <div
                  style={{
                    alignItems: "center",
                    justifyContent: "center",
                    width: "100%",
                  }}
                >
                  <Spacer height={sizes[4]} />
                  <ButtonSecondary onClick={onOkButton} style={{ width: "100%" }}>
                    {primaryButtonText || "Try full experience"}
                  </ButtonSecondary>

                  <Spacer height={sizes[4]} />
                  <ButtonPrimary onClick={onExitRole} style={{ width: "100%" }}>
                    {secondaryButtonText || "Exit role-play"}
                  </ButtonPrimary>
                </div>
              )}
            </Content>
          </CenterView>
        </BorderView>
      </Container>
    </Modal>
  );
};

export default MissionCompleteModal;
