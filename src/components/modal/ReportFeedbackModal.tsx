import React, { useCallback } from "react";
import FeedbackModal from "./FeedbackModal";
import { StarRating } from "../feedback/StarRating";
import styled from "styled-components";

const Container = styled.div`
  z-index: 1;
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: 20px;
`;

const ReportFeedbackModal = ({ conversationId }: { conversationId: string }) => {
  const [starRating, setStarRating] = React.useState<number>(0);
  const [modalOpen, setModalOpen] = React.useState(false);

  const handleChange = useCallback((value: number) => {
    setModalOpen(true);
    setStarRating(value);
  }, []);

  // console.log("ReportFeedbackModal");
  return (
    <Container>
      <StarRating
        title="How do you feel about this conversation?"
        smallTitle={true}
        value={starRating}
        readOnly={false}
        onChange={handleChange}
      />
      <FeedbackModal
        starRating={starRating}
        conversationId={conversationId}
        visible={modalOpen}
        setVisible={setModalOpen}
        handleUserUpdate={(selectedStar) => selectedStar && setStarRating(selectedStar)}
        feedbackType="REPORT"
        title="How do you feel about this conversation?"
      />
    </Container>
  );
};

export default ReportFeedbackModal;
