import React, { useEffect, useState } from "react";
import Modal from "./BaseModal";
import styled from "styled-components";
import { colors } from "../CubeBaseElement";
import { homePageBg } from "../../assets";
import FullScreenPage from "../FullScreenPage";

interface CountdownCircleProps {
  initialCount?: number;
  onComplete: () => void;
  visible: boolean;
}

const CountdownCircleModal: React.FC<CountdownCircleProps> = ({ initialCount = 3, onComplete, visible }) => {
  const [count, setCount] = useState(initialCount);

  useEffect(() => {
    setCount(initialCount);
  }, [initialCount]);

  useEffect(() => {
    if (count > 0) {
      const timer = setTimeout(() => setCount((count) => count - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      onComplete();
    }
  }, [count, onComplete]);

  if (!visible) {
    return null;
  }

  return (
    <FullScreenPage>
      <Container>
        <ImgBg>
          <Circle>
            <CountText>{count}</CountText>
          </Circle>
        </ImgBg>
      </Container>
    </FullScreenPage>
  );
};

const ImgBg = styled.div`
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: url(${homePageBg}) no-repeat;
  background-size: cover;
  display: flex;
`;

const Container = styled.div`
  display: flex;
  flex: 1;
  width: 100%;
  height: 100vh;
  background-color: rgba(24, 48, 40, 0.7);
  justify-content: center;
  align-items: center;
`;

const Circle = styled.div`
  width: 288px;
  height: 288px;
  border-radius: 144px;
  justify-content: center;
  align-items: center;
  display: flex;
  border-style: solid;
  border-width: 10px;
  border-color: ${colors.fwdOrange[50]};
`;

const CountText = styled.label`
  font-size: 150px;
  color: ${colors.fwdOrange[50]};
  font-weight: bold;
  margin-top: -10px;
`;

export default CountdownCircleModal;
