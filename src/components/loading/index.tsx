import React from 'react';
import styled from 'styled-components';
import spinner from '../../assets/spinner.svg';


const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const SpinnerImage = styled.img`
  width: 40px;
  height: 40px;
  animation: spin 3s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

interface LoadingSectionProps {
  text?: string;
  loading?: boolean;
}

const LoadingSection: React.FC<LoadingSectionProps> = ({ text = "Loading...", loading }) => {
  return (
    <LoadingContainer>
      <SpinnerImage src={spinner} alt="Loading..." />
    </LoadingContainer>
  );
};

export default LoadingSection;
