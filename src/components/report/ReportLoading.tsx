import bg from "../../assets/report-bg3.png";
import bg2 from "../../assets/tree.png";
import loadingGif from "../../assets/loading-report2.gif";
import { Quote } from "./Quote";
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { sizes } from "../CubeBaseElement";

export const ReportLoading = () => {
  const { t } = useTranslation("ecoach");
  return (
    <ReportLoadingContainer className="report-loading min-h-screen">
      <ReportLoadingContent
        className={
          "flex w-full h-full min-h-screen bg-[#fffbf6] items-center justify-center"
        }
      >
        <ImageBgr1
          alt={"loading"}
          src={bg}
          width={8000}
          className={
            "bg-cover bg-bottom self-end absolute left-0 right-0 h-auto bg1"
          }
        />
        <ImageBgr2
          alt={"loading"}
          src={bg2}
          width={200}
          className={"absolute left-0 bottom-0 bg2"}
        />
        <TextContainer
          className={
            "absolute top-[15%] max-w-[1520px] text-center flex flex-col items-center justify-center text-container"
          }
        >
          <Text>{t("preparingYourReport")}</Text>

          <ImageBgr3
            alt={"loading"}
            src={loadingGif}
            width={300}
            className={"my-8 loading-gif"}
          />

          <Quote />
        </TextContainer>
      </ReportLoadingContent>
    </ReportLoadingContainer>
  );
};

const ReportLoadingContainer = styled.div`
  min-height: calc(100vh - 56px);
  width: 100vw;
  overflow: hidden;
`;

const ReportLoadingContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: calc(100vh);
  background-color: #fffbf6;
`;

const TextContainer = styled.div`
  position: absolute;
  top: 15%;
  max-width: 1520px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 ${sizes[4]}px;

  @media (min-width: 769px) {
    max-width: calc(100vw - 2rem);
  }

  @media (min-width: 913px) {
    max-width: calc(100vw - 2rem);
  }
`;

const ImageBgr1 = styled.img`
  position: absolute;
  left: 0;
  right: 0;
  height: auto;
  background-size: cover;
  background-position: bottom;
  align-self: end;
`;

const ImageBgr2 = styled.img`
  position: absolute;
  left: 0;
  right: 0;
  width: 10vw;
`;

const ImageBgr3 = styled.img`
  margin-top: 2rem;
  margin-bottom: 2rem;
  width: 30vw;
  max-width: 300px;
`;

const Text = styled.h3`
  font-weight: bold;
  font-size: 20px;

  @media (min-width: 769px) {
    font-size: 1.5rem;
  }

  @media (min-width: 913px) {
    font-size: 1.7rem;
  }
`;
