import { useEffect, useState } from "react";
import styled from "styled-components";

export const Quote = () => {
  const [currentQuote, setCurrentQuote] = useState(getRandomQuote());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote(getRandomQuote());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <QuoteText>“{currentQuote.quote}”</QuoteText>
    </>
  );
};

const QuoteText = styled.h3`
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 2.5rem;

  @media (min-width: 769px) {
    font-size: 1.5rem;
  }

  @media (min-width: 913px) {
    font-size: 1.7rem;
  }
`;

const AuthorText = styled.h3`
  font-weight: normal;
  font-size: 1.5rem /* 24px */;
  line-height: 2rem /* 32px */;

  @media (min-width: 769px) {
    font-size: 1.25rem;
  }

  @media (min-width: 913px) {
    font-size: 1.45rem;
  }
`;

const getRandomQuote = () => {
  const randomIndex = Math.floor(Math.random() * quotes.length);
  return quotes[randomIndex];
};

const quotes = [
  {
    "quote": "Langkah kecil hari ini, Bisa jadi lompatan nanti.",
    "author": ""
  },
  {
    "quote": "Belajar tak harus langsung bisa, Yang penting terus coba dan percaya.",
    "author": ""
  },
  {
    "quote": "Hari ini mungkin berat terasa, Tapi ingat, kamu bukan orang biasa.",
    "author": ""
  },
  {
    "quote": "Jalanmu boleh lambat, Tapi setiap langkah tetap hebat.",
    "author": ""
  },
  {
    "quote": "Capek itu wajar, jangan dibantah, Tandanya kamu lagi tumbuh jadi luar biasa.",
    "author": ""
  },
  {
    "quote": "Mimpi besar bukan buat ditakuti, Tapi buat diburu, sedikit demi sedikit.",
    "author": ""
  },
  {
    "quote": "Hari ini mungkin belum juara, Tapi kamu lagi proses jadi luar biasa.",
    "author": ""
  },
  {
    "quote": "Gagal itu bukan akhir cerita, Cuma jeda biar kamu makin siap luar biasa.",
    "author": ""
  }
];