import React from "react";
import {
  CircleCheckIcon,
  CircleXIcon,
  Container,
  Content,
  H1,
  H3,
  H4,
  ImprovementContainer,
  ImprovementH3,
  ImprovementH31,
  ImprovementItem,
  ThingDidWellContainer,
  ThingDidWellItem,
} from "./styled";

export type SkillDetail = {
  reason: string;
  improvement: string;
  assessment: string;
  excerpt: string;
  atom_summary: string;
  try_next_time: string;
};

export type SkillSetDetails = {
  applied_product_knowledge: { [key: string]: SkillDetail }[];
  customer_relationship: { [key: string]: SkillDetail }[];
  objection_handling_closing: { [key: string]: SkillDetail }[];
  communication_skills: { [key: string]: SkillDetail }[];
  customer_discovery: { [key: string]: SkillDetail }[];
};

export type ReportDetails =
  | {
      overall_score: string;
      applied_product_knowledge_score: string;
      customer_discovery_score: string;
      customer_relationship_score: string;
      objection_handling_closing_score: string;
      communication_skills_score: string;
      well_done_comment: string;
      improvement_comment: string;
      skill_set_details: SkillSetDetails;
    }
  | Record<string, never>;

enum ReportStatus {
  NA = "N/A",
  YES = "YES",
  NO = "NO",
}

type Improvement = {
  improvement: string;
  try_next_time: string;
};

export const extractScore = (scoreString?: string) => {
  if (!scoreString) return 0;
  try {
    const extractedString = scoreString.split(" / ")[0];
    const extractedNumber = parseFloat(extractedString);
    return Math.floor(extractedNumber);
  } catch (error) {
    return 0;
  }
};

export const renderSkillTitle = (skill: string) => {
  return (
    <>
      {skill === "applied_product_knowledge" && "Product Knowledge"}
      {skill === "customer_relationship" && "Customer Relationship"}
      {skill === "objection_handling_closing" && "Objection Handling & Closing"}
      {skill === "communication_skills" && "Communication & Analysis"}
      {skill === "process_compliance" && "Process Compliance"}
      {skill === "customer_discovery" && "Customer Discovery"}
    </>
  );
};

type ReportDetailsPagProps = {
  skillSet: { [key: string]: SkillDetail }[];
  title: string;
  onBack?: () => void;
};

const ReportDetailsPage: React.FC<ReportDetailsPagProps> = ({ title, skillSet, onBack }) => {
  const processData = (skillSet: { [key: string]: SkillDetail }[]) => {
    const thingsDidWell: string[] = [];
    const thingsDidNotDoWell: string[] = [];
    const improvements: Improvement[] = [];

    skillSet.forEach((item) => {
      const key = Object.keys(item)[0];
      const entry: SkillDetail = item[key];
      if (entry.assessment !== ReportStatus.NA && entry.atom_summary !== ReportStatus.NA) {
        if (entry.assessment == ReportStatus.YES) {
          thingsDidWell.push(entry.atom_summary);
        } else if (entry.assessment == ReportStatus.NO) {
          thingsDidNotDoWell.push(entry.atom_summary);
          if (entry.improvement !== ReportStatus.NA && entry.try_next_time !== ReportStatus.NA) {
            improvements.push({
              improvement: entry.improvement,
              try_next_time: entry.try_next_time,
            });
          }
        }
      }
    });

    return { thingsDidWell, thingsDidNotDoWell, improvements };
  };

  const { thingsDidWell, thingsDidNotDoWell, improvements } = processData(skillSet);

  return (
    <>
      <Container className={"w-full text-left items-center grid grid-cols-[2fr,3fr] gap-8 mb8 md:px-4"}>
        <Content className={"h-full relative"}>
          <H1 className={"text-[25px] text-[#183028] font-bold mb12"}>{renderSkillTitle(title)}</H1>
          {thingsDidWell?.length ? (
            <>
              <H3 className={"text-[16px] text-[#183028] font-bold mb4 ml4"}>Things you did well:</H3>
              <ThingDidWellContainer className={"flex items-start ml4 flex-col gap-2"}>
                {thingsDidWell.map((thing) => {
                  return (
                    <ThingDidWellItem key={thing} className={"flex items-start justify-start gap-2 text-[14px]"}>
                      <CircleCheckIcon className={"-mt-1 text-white fill-[#6ECEB2] mr-1 text-xl min-w-8"} size={32} />{" "}
                      <span>{thing}</span>
                    </ThingDidWellItem>
                  );
                })}
              </ThingDidWellContainer>
              <br />
            </>
          ) : null}
          {thingsDidNotDoWell?.length ? (
            <>
              <H3 className={"text-[16px] text-[#183028] font-bold mb4 ml4"}>Things you did not do well:</H3>
              <ThingDidWellContainer className={"flex items-start ml4 flex-col gap-2"}>
                {thingsDidNotDoWell.map((thing) => {
                  return (
                    <ThingDidWellItem key={thing} className={"flex items-start justify-start gap-2 text-[14px]"}>
                      <CircleXIcon className={"-mt-1 text-white fill-[#B30909] mr-1 text-xl min-w-8"} size={32} />{" "}
                      <span>{thing}</span>
                    </ThingDidWellItem>
                  );
                })}
              </ThingDidWellContainer>
            </>
          ) : null}
        </Content>
        <div className={"rounded-lg flex-1 h-full p-1 text-left"} style={{}}>
          <ImprovementH3 className={"text-[16px] text-[#183028] font-bold mb6"} style={{ marginBottom: "1.5rem" }}>
            Improvements:
          </ImprovementH3>
          {improvements.map((improvement) => {
            return (
              <ImprovementContainer className={"mb2"} key={improvement.improvement}>
                <H4 className={"text-[14px] text-[#183028] font-normal"}>
                  <span>{improvement.improvement}</span>
                </H4>
                {improvement.try_next_time && (
                  <ImprovementItem className={"mt4 p4 bg-[#F8F9F9]"}>
                    <ImprovementH31 className={"text-[16px] text-[#183028] font-bold mb1"}>
                      Try saying this next time:
                    </ImprovementH31>
                    <H4 className={"text-[14px] text-[#183028] font-normal"}>{improvement.try_next_time}</H4>
                  </ImprovementItem>
                )}
              </ImprovementContainer>
            );
          })}
        </div>
      </Container>
    </>
  );
};

export default ReportDetailsPage;
