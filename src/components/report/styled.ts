import styled from "styled-components";
import { ChevronRight, CircleCheck, CircleX, WeatherIcon } from "../icons";
import { device } from "@styles/media";

export const Container = styled.div`
  width: 100%;
  text-align: left;
  align-items: center;
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 2rem;
  margin-bottom: 2rem;
  margin-top: 6rem;
  padding-left: 1rem /* 16px */;
  padding-right: 1rem /* 16px */;

  @media ${device.mobile} {
    margin-top: 1rem;
    grid-template-columns: 1fr;
  }
`;

export const Content = styled.div`
  height: 100%;
  position: relative;
`;

export const H1 = styled.h1`
  font-size: 25px;
  color: #183028;
  font-weight: bold;
  margin-bottom: 3rem;
`;

export const H3 = styled.h3`
  font-size: 1rem;
  color: #183028;
  font-weight: bold;
  margin-bottom: 1rem;
  margin-left: 1rem;
`;

export const ThingDidWellContainer = styled.div`
  display: flex;
  align-items: flex-start;
  margin-left: 1rem;
  flex-direction: column;
  gap: 0.5rem;
`;

export const ThingDidWellItem = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 0.5rem;
  font-size: 14px;
`;

export const CircleCheckIcon = styled(CircleCheck)`
  margin-top: -0.25rem;
  color: #fff;
  fill: #6eceb2;
  margin-right: 0.25rem;
  font-size: 1.25rem /* 20px */;
  line-height: 1.75rem /* 28px */;
  min-width: 2rem /* 32px */;
`;

export const CircleXIcon = styled(CircleX)`
  margin-top: -0.25rem;
  color: #fff;
  fill: #b30909;
  margin-right: 0.25rem;
  font-size: 1.25rem /* 20px */;
  line-height: 1.75rem /* 28px */;
  min-width: 2rem /* 32px */;
`;

export const ImprovementContainer = styled.div`
  margin-bottom: 0.5rem;
`;

export const H4 = styled.h4`
  font-size: 14px;
  color: #183028;
  font-weight: normal;
`;

export const ImprovementItem = styled.div`
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9f9;
`;

export const ImprovementH3 = styled.h3`
  font-size: 1rem;
  color: #183028;
  font-weight: bold;
  margin-bottom: 1.5rem;
`;

export const ImprovementH31 = styled.h3`
  font-size: 1rem;
  color: #183028;
  font-weight: bold;
  margin-bottom: 0.25rem;
`;

/**
 * Report
 */

export const ChevronRightIcon = styled(ChevronRight)`
  color: #e87722;
  margin-left: 0.25rem;
  margin-top: 0.25rem;
  width: 1rem;

  @media (min-width: 413px) {
    width: 1rem;
  }
`;

export const ScoreBarInner = styled.div`
  flex: 1 1 0;
  position: relative;
  height: 100%;
  width: 100%;
`;

export const ScoreNumber = styled.div`
  position: absolute;
  bottom: 0;
  width: 100%;
  font-size: 1rem;

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
    font-size: 1rem;
  }

  @media (min-width: 413px) {
    font-size: 1.5rem;
  }

  @media (min-width: 479px) {
  }

  @media (min-width: 769px) {
  }

  @media (min-width: 913px) {
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
    font-size: 1.7rem;
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    font-size: 2rem;
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const ScoreBar = styled.div`
  border-radius: 0.75rem /* 12px */;
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
`;

export const SeeMoreLink = styled.a`
  color: #e87722;
  text-align: left;
  display: flex;
  align-items: center;
  margin-left: 1rem;
  font-size: 0.65rem;
  line-height: 0.6rem;
  letter-spacing: -0.03rem;

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
  }

  @media (min-width: 413px) {
    font-size: 0.7rem;
    line-height: 0.7rem;
  }

  @media (min-width: 479px) {
    font-size: 0.8rem;
    line-height: 0.9rem;
  }

  @media (min-width: 769px) {
  }

  @media (min-width: 913px) {
    font-size: 0.85rem;
    line-height: 0.85rem;
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
    font-size: 1.2rem;
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    font-size: 1.5rem;
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const ReportContainer = styled.div`
  min-height: calc(100vh - 56px);
`;

export const ReportInner = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 56px);
  background-color: #fffbf6;
`;

export const ReportBackground = styled.img`
  background-size: cover;
  background-position: bottom;
  align-self: end;
  position: absolute;
  left: 0;
  right: 0;
  height: auto;
`;

export const ReportContent = styled.div`
  display: flex;
  flex-direction: column;
  align-self: center;
  justify-content: center;
  margin-top: 1rem;
  z-index: 10;
`;

export const ReportContentInner = styled.div`
  width: 100%;
  display: flex;
  align-self: center;
  justify-content: center;

  @media (min-width: 413px) {
    justify-content: center;
  }

  @media (min-width: 479px) {
    justify-content: space-between;
  }
`;

export const YourReportContainer = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
  align-self: flex-start;

  @media (min-width: 413px) {
    align-items: center;
  }
`;

export const YourReportTitle = styled.h1`
  font-weight: bold;
  font-size: 0.7rem;

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
    font-size: 1rem;
  }

  @media (min-width: 413px) {
    font-size: 1.2rem;
  }

  @media (min-width: 479px) {
    font-size: 1.5rem;
  }

  @media (min-width: 769px) {
  }

  @media (min-width: 913px) {
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    font-size: 4.5rem;
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const WeatherIconElement = styled(WeatherIcon)`
  margin: 0;
  transform: scale(1) !important;
  max-width: 80px;
  max-height: 60px;

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
  }

  @media (min-width: 413px) {
    margin: 0;
    transform: scale(1) !important;
    max-width: 80px;
    max-height: 60px;
  }

  @media (min-width: 479px) {
    margin: 0;
    transform: scale(1.2) !important;
    max-width: 100px;
    max-height: 80px;
  }

  @media (min-width: 769px) {
    margin: 0 0 0 1rem;
    transform: scale(1.4) !important;
    max-width: none;
    max-height: none;
  }

  @media (min-width: 913px) {
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    transform: scale(1.4) !important;
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const StartContainer = styled.div`
  margin-bottom: 1rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0.75);
  max-width: 80px;
  max-height: 60px;

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
  }

  @media (min-width: 413px) {
    transform: scale(0.75);
    max-width: 80px;
    max-height: 60px;
  }

  @media (min-width: 479px) {
    transform: scale(0.75);
    max-width: 100px;
    max-height: 80px;
  }

  @media (min-width: 769px) {
    transform: scale(0.75);
    max-width: none;
    max-height: none;
  }

  @media (min-width: 913px) {
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    transform: scale(1) !important;
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const OverallScoreTitle = styled.h1`
  color: #8b9793;
  font-weight: bold;
  font-size: 0.7rem;
  line-height: 0.7rem;

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
    font-size: 0.8rem;
    line-height: 0.8rem;
  }

  @media (min-width: 413px) {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  @media (min-width: 479px) {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  @media (min-width: 769px) {
  }

  @media (min-width: 913px) {
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    font-size: 2.25rem;
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const CurrentScoreTitle = styled.h1`
  font-weight: bold;
  line-height: 15px;
  text-align: left;

  & > span:first-child {
    font-size: 0.8rem;
    color: #183028;
    margin-right: 0.25rem;
  }

  & > span + span {
    line-height: 2rem /* 32px */;
    color: #8b9793;
    font-size: 0.7rem;
  }

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
    line-height: 20px;

    & > span:first-child {
      font-size: 1.5rem;
    }
  }

  @media (min-width: 413px) {
    line-height: 20px;

    & > span:first-child {
      font-size: 2rem;
    }

    & > span + span {
      font-size: 1rem;
    }
  }

  @media (min-width: 479px) {
    line-height: 30px;

    & > span:first-child {
      font-size: 2.5rem;
    }
  }

  @media (min-width: 769px) {
  }

  @media (min-width: 913px) {
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    line-height: 100px;

    & > span:first-child {
      font-size: 6rem;
    }

    & > span + span {
      font-size: 1.5rem;
    }
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const ReportItem = styled.div`
  display: grid;
  gap: 0.25rem;
  grid-template-columns: repeat(5, minmax(320px, 1fr));
  min-height: 625px;
`;

export const ReportContentContainer = styled.div`
  width: 100%;
  text-align: center;
  align-items: center;
  border-radius: 0.5rem;
  margin-bottom: 0.25rem;
  max-width: calc(100vw - 2rem) !important;

  & ${ReportItem} {
    grid-template-columns: repeat(5, minmax(40px, 1fr));
    min-height: 300px;
  }

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
  }

  @media (min-width: 413px) {
    max-width: calc(100vw - 2rem) !important;

    & ${ReportItem} {
      grid-template-columns: repeat(5, minmax(60px, 1fr));
      min-height: 425px;
    }
  }

  @media (min-width: 479px) {
  }

  @media (min-width: 769px) {
    & ${ReportItem} {
      grid-template-columns: repeat(5, minmax(120px, 1fr));
    }
  }

  @media (min-width: 913px) {
    max-width: calc(100vw - 2rem) !important;
    //min-width: 1020px !important;

    & ${ReportItem} {
      grid-template-columns: repeat(5, minmax(160px, 1fr));
    }
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
    & ${ReportItem} {
      grid-template-columns: repeat(5, minmax(220px, 1fr));
      min-height: 525px;
    }
  }

  @media (min-width: 1367px) {
    & ${ReportItem} {
      grid-template-columns: repeat(5, minmax(260px, 1fr));
      min-height: 525px;
    }
  }

  @media (min-width: 1536px) {
    & ${ReportItem} {
      grid-template-columns: repeat(5, minmax(290px, 1fr));
      min-height: 525px;
    }
  }

  @media (min-width: 1677px) {
    & ${ReportItem} {
      grid-template-columns: repeat(5, minmax(320px, 1fr));
      min-height: 625px;
    }
  }

  @media (min-width: 1920px) {
    max-width: 1820px !important;
    min-width: 1820px !important;
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const ReportItemContainer = styled.div`
  flex: 1 1 0;
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 1.5rem;
  box-shadow: 0 0 10px 0 #00000040;

  &:before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 13px;
    padding: 6px;
    background: linear-gradient(126.57deg, #6eceb2 11.93%, #fed241 33.49%, #e87722 73.6%, #0097a9 100%);
    -webkit-mask:
      linear-gradient(126.57deg, #6eceb2 11.93%, #fed241 33.49%, #e87722 73.6%, #0097a9 100%) content-box,
      linear-gradient(126.57deg, #6eceb2 11.93%, #fed241 33.49%, #e87722 73.6%, #0097a9 100%);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
`;

export const ReportGridContainer = styled.div`
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow: hidden;
`;

export const SkillContainer = styled.div`
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  height: calc(100% - 4rem);
  cursor: pointer;
  margin-top: 2.5rem;
`;

export const ScoreBarParent = styled.div`
  flex: 0 0 1;
  width: 100%;
  height: 100%;
`;

export const ScoreBarContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
  }

  @media (min-width: 413px) {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  @media (min-width: 479px) {
  }

  @media (min-width: 769px) {
  }

  @media (min-width: 913px) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const SkillTitleContainer = styled.div`
  text-align: center;
  font-size: 0.7rem;
  line-height: 0.7rem;
  letter-spacing: -0.03rem;
  font-weight: 500;

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
  }

  @media (min-width: 413px) {
    font-size: 0.75rem;
    line-height: 0.8rem;
  }

  @media (min-width: 479px) {
    font-size: 0.9rem;
    line-height: 1rem;
  }

  @media (min-width: 769px) {
  }

  @media (min-width: 913px) {
    font-size: 1rem;
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
    font-size: 1.5rem;
    line-height: 1.7rem;
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    font-size: 1.8rem;
    line-height: 2rem;
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const ButtonContainer = styled.div`
  width: 100%;
  text-align: left;
`;

export const ButtonInner = styled.div`
  display: flex;
  align-items: flex-start;
`;

export const FinishButton = styled.button`
  max-width: 100%;
  width: 100%;
  margin: 1rem auto auto;
  color: #fff;
  background-color: #e87722;
  font-weight: 500;
  border-radius: 6px;
  padding: 0.75rem 1.25rem;
  height: 3rem;
  font-size: 1.25rem;
  line-height: 1.5rem;

  &:hover {
    background-color: #e87722;
  }

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  @media (min-width: 319px) {
  }

  @media (min-width: 359px) {
    height: 3.2rem;
    font-size: 1.2rem;
  }

  @media (min-width: 413px) {
    height: 4.5rem;
    font-size: 1.25rem;
  }

  @media (min-width: 479px) {
  }

  @media (min-width: 769px) {
  }

  @media (min-width: 913px) {
    height: 5.5rem;
    font-size: 1.25rem;
  }

  @media (min-width: 1024px) {
  }

  @media (min-width: 1181px) {
  }

  @media (min-width: 1281px) {
  }

  @media (min-width: 1367px) {
  }

  @media (min-width: 1536px) {
  }

  @media (min-width: 1677px) {
    height: 8.5rem;
    font-size: 2.25rem;
  }

  @media (min-width: 1920px) {
  }

  @media (min-height: 820px) {
  }

  @media (min-height: 1024px) {
  }

  @media (min-height: 1200px) {
  }
`;

export const InforPane = styled.div`
  width: 100%;
  text-align: left;
  margin-bottom: 0.5rem;
`;

export const InforPaneInner = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
`;
export const InforPaneItem = styled.div`
  font-size: 8px;
  color: rgb(209, 213, 219);

  & > span {
    font-weight: 600;
  }
`;
