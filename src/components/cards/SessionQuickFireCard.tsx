import React, { useMemo } from "react";
import styled from "styled-components";
import {
  colors,
  H3,
  H7,
  H8,
  Label,
  sizes,
  SmallLabel,
} from "@components/CubeBaseElement";
import { mapConversationTypeToProductFlowType } from "@/utils/conversationHelper";
import moment from "moment";
import { ConversationData, ConversationType } from "../../types/ecoach-custom";
import ChevronRightIcon from "@assets/icons/ChevronRightIcon";
import { device } from "@styles/media";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@store/hooks";
import { useTranslation } from "react-i18next";
import { ECOACH_COUNTRY } from "@/@custom-types/ecoach";
import { extractScore } from "@/utils/extractScore";

const Card = styled.div`
  width: 100%;
  background-color: #1a1a1a;
  border-radius: ${sizes[2]}px;
  border: 1px solid #4d4d4d;
  height: 80px;
  padding: ${sizes[3]}px ${sizes[2]}px ${sizes[3]}px ${sizes[3]}px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: ${sizes[4]}px;
  gap: ${sizes[2]}px;
  @media ${device.noMobile} {
    height: auto;
    margin-bottom: 0;
  }
`;

const SSColumn = styled.div`
  display: flex;
  flex-direction: column;
  margin-right: ${sizes[4]}px;
  flex-shrink: 0;
  min-width: 64px;
`;

const TitleColumn = styled.div`
  display: flex;
  flex-direction: column;
  margin-right: ${sizes[4]}px;
  flex-grow: 1;
  @media ${device.mobile} {
    margin-left: ${sizes[0]}px;
    margin-right: ${sizes[0]}px;
  }
`;

const NumberColumn = styled.div`
  display: flex;
  flex-direction: column;
  margin-left: ${sizes[4]}px;
  margin-right: ${sizes[4]}px;
  flex-shrink: 0;
  @media ${device.mobile} {
    margin-right: ${sizes[1]}px;
    margin-left: ${sizes[1]}px;
  }
`;

const CenteredText = styled(H7)`
  text-align: center;
`;

const CurrentScore = styled(H3)`
  @media (max-width: 414px) {
    font-size: 28px;
  }
`;

const TotalScore = styled.span`
  color: ${colors.fwdDarkGreen[20]};
  @media (max-width: 414px) {
    font-size: 0.85rem;
  }
`;

type SessionCardProps = {
  session: ConversationData;
  sessionNumber: number | "";
};

const SessionQuickFireCard = ({ session, sessionNumber }: SessionCardProps) => {
  const { t } = useTranslation("ecoach");
  const navigate = useNavigate();
  const {
    conversation_id,
    datetime,
    difficulty,
    product_selection,
    report,
    conversation_type,
    score,
  } = session;
  const sessionScore = extractScore(conversation_type, report, score);
  const productConfig = useAppSelector((state) => state.ecoach.productConfig);
  const quickfireProductConfig = useAppSelector((state) => state.ecoach.quickfireProductConfig);
  const objectionHandlingProductConfig = useAppSelector((state) => state.ecoach.objectionHandlingProductConfig);

  const productName = useMemo(() => {
    let moduleConfig = productConfig;
    if (conversation_type === ConversationType.QUICKFIRE) {
      moduleConfig = quickfireProductConfig;
    } else if (conversation_type === ConversationType.OBJECTION_HANDLING) {
      moduleConfig = objectionHandlingProductConfig;
    }
    const productForCurrentCountry = moduleConfig?.[ECOACH_COUNTRY];
    return (
      productForCurrentCountry?.find(
        (e) => e.product_code === product_selection
      )?.product_name ||
      product_selection
        ?.split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );
  }, [product_selection, productConfig]);

  const onPress = () => {
    const productFlowType = mapConversationTypeToProductFlowType(conversation_type);
    navigate(`/report?conversationId=${conversation_id}&productFlowType=${productFlowType}`, {
      state: { report: session },
    });
  };

  return (
    <Card onClick={onPress}>
      <SSColumn>
        <SmallLabel fontWeight="normal" color={colors.fwdGrey[50]}>
          {moment(datetime).format("DD/MM")}
        </SmallLabel>
        <H8 fontWeight="bold" color={colors.white}>
          {t("ssID")} {sessionNumber.toString() || "0"}
        </H8>
      </SSColumn>

      <TitleColumn>
        {conversation_type &&
          [
            ConversationType.FULL_EXPERIENCE,
          ].includes(conversation_type) && (
            <SmallLabel fontWeight="normal" color={colors.white}>
              {difficulty === "1" ? t("beginner") : t("expert")}
            </SmallLabel>
          )}
        {productName && (
          <Label fontWeight="bold" color={colors.white}>
            {productName}
          </Label>
        )}
      </TitleColumn>

      <NumberColumn>
        <CenteredText fontWeight={"bold"} color={colors.white}>
          <CurrentScore fontWeight={"bold"} color={colors.white}>
            {sessionScore}
          </CurrentScore>
          <TotalScore>/100</TotalScore>
        </CenteredText>
      </NumberColumn>
      <div style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: 30,
        height: 30,
        flexShrink: 0,
      }}>
        <ChevronRightIcon fill={colors.brilBlue[100]} size={18} />
      </div>
    </Card>
  );
};

export default SessionQuickFireCard;
