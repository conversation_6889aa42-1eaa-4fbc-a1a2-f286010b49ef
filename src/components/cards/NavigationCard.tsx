import React, { useTransition } from "react";
import styled from "styled-components";
import { Body, H6, sizes, SmallLabel, Spacer } from "../CubeBaseElement";
import {
  cheveronRightInCircle,
  mobileQFCard,
  mobileRPCard,
  mobileOHCard,
  tabletQFCard,
  tabletRPCard,
  tabletOHCard,
} from "../../assets";
import { device } from "@styles/media";
import useWindowResize from "@hooks/use-window-resize";
import { useTranslation } from "react-i18next";

const Container = styled.div<{ $backgroundColor: string }>`
  // height: 270px;
  height: 121px;
  display: flex;
  flex: 1;
  flex-direction: row;
  padding: ${sizes[4]}px;
  border-radius: ${sizes[4]}px;
  position: relative;
  cursor: pointer;
  background-color: ${({ $backgroundColor }) => $backgroundColor};
  border: 1px solid #c9dbe7;
  // width: 166px;
  width: 100%;
  /* overflow: auto; */
  @media ${device.noMobile} {
    height: 144px;
    width: 100%;
  }
`;
const CardImage = styled.img`
  /* position: absolute; */
  bottom: 0;
  right: ${sizes[2]}px;
  max-width: 20%;
  max-height: 90%;
  width: auto;
  height: auto;
`;

const Circle = styled.img`
  margin-top: ${sizes[2]}px;
  width: 32px;
`;

const Tag = styled.div`
  /* position: absolute; */
  display: flex;
  top: ${sizes[3]}px;
  left: ${sizes[3]}px;
  width: 68px;
  height: ${sizes[6]}px;
  border-radius: ${sizes[5]}px;
  background-color: white;
  align-items: center;
  justify-content: center;
`;

const Title = styled(H6)`
  /* width: 133px;

  @media ${device.noMobile} {
    width: 100%;
  } */
`;

const Description = styled(Body)`
  /* width: 139px; */
  /* margin-bottom: 65px; */
  /* @media ${device.noMobile} {
    width: 213px;
    margin-bottom: 0px;
  } */
`;

const TextContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  /* max-width: 80%; */
  gap: ${sizes[2]}px;
  padding: ${sizes[2]}px 0;
`;

export enum NavigationCardType {
  Quickfire = "quickfire",
  SalesRolePlay = "salesRolePlay",
  ObjectionHandling = "objectionHandling",
}

type NavigationCardProps = {
  navigationCardType: NavigationCardType;
  onPress: () => void;
};

const NavigationCard = ({
  navigationCardType,
  onPress,
}: NavigationCardProps) => {
  const { width } = useWindowResize();
  const isMobile = width <= 768;
  const {t} = useTranslation("ecoach");
  let backgroundColor = "#004374";
  let timeText = "";
  let modeText = "";
  let descriptionText = "";
  let durationText = "";
  let cardImg = mobileQFCard;

  if (navigationCardType === NavigationCardType.SalesRolePlay) {
    backgroundColor = "#004374";
    timeText = t("rpMin");
    durationText = t("rpMin");
    modeText = t("salesRolePlay");
    descriptionText = t("salesRolePlayDesc");
    if (isMobile) {
      cardImg = mobileRPCard;
    } else {
      cardImg = tabletRPCard;
    }
  } else if (navigationCardType === NavigationCardType.Quickfire) {
    backgroundColor = "#0179CE";
    timeText = t("pkMin");
    durationText = t("pkMin");
    modeText = t("quickfire");
    descriptionText = t("quickFireDesText");
    if (isMobile) {
      cardImg = mobileQFCard;
    } else {
      cardImg = tabletQFCard;
    }
  } else if (navigationCardType === NavigationCardType.ObjectionHandling) {
    backgroundColor = "#34AAFF";
    timeText = t("ohMin");
    durationText = t("ohMin");
    modeText = t("objectionHandling");
    descriptionText = t("objectionHandlingDesc");
    if (isMobile) {
      cardImg = mobileOHCard;
    } else {
      cardImg = tabletOHCard;
    }
  }

  return (
    <div
      style={{
        backgroundColor: backgroundColor,
        padding: `0 ${sizes[4]}px`,
        borderRadius: sizes[4],
        display: "flex",
        flexDirection: "row",
        alignItems: "flex-end",
        justifyContent: "space-between",
        cursor: "pointer",
        width: "100%",
        border: "1px solid #c9dbe7",
      }}
      onClick={onPress}
    >
      <div style={{marginTop: -12}}>
        <Tag>
          <SmallLabel fontWeight="bold" color={"#E9690C"}>
            {timeText}
          </SmallLabel>
        </Tag>
        <TextContainer>
          <Title fontWeight="bold" color={"white"}>
            {modeText}
          </Title>
          <Description fontWeight="normal" color={"white"}>
            {descriptionText}
          </Description>
          <Spacer height={sizes[2]} />
        </TextContainer>
      </div>
      <CardImage src={cardImg} alt="" />
    </div>
  );

  return (
    <Container $backgroundColor={backgroundColor} onClick={onPress}>
      <Tag>
        <SmallLabel fontWeight="bold" color={"#E9690C"}>
          {timeText}
        </SmallLabel>
      </Tag>
      <TextContainer>
        <Title fontWeight="bold" color={"white"}>
          {modeText}
        </Title>
        <Description fontWeight="normal" color={"white"}>
          {descriptionText}
        </Description>
      </TextContainer>
      <CardImage src={cardImg} alt="" />
    </Container>
  );
};

export default NavigationCard;
