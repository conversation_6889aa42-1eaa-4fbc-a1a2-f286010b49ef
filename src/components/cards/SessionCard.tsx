import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@store/hooks";
import styled from "styled-components";
import { colors, H3, H8, Label, sizes, SmallLabel } from "@components/CubeBaseElement";
import { ConversationData, ECOACH_COUNTRY } from "../../types/ecoach-custom";
import moment from "moment";
import ChevronRightIcon from "@components/icons/ChevronRightIcon";
import { mapConversationTypeToProductFlowType } from "@/utils/conversationHelper";
import { extractStringScore } from "@/utils/extractScore";

const Card = styled.div`
  width: 100%;
  background-color: ${colors.white};
  border-radius: ${sizes[3]}px;
  border: 1px solid ${colors.fwdOrange[50]};
  height: auto;
  padding: ${sizes[3]}px ${sizes[4]}px;
  cursor: pointer;
`;

const HorizontalView = styled.div`
  display: flex;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

const ContentView = styled.div`
  display: flex;
  width: 55%;
  flex-direction: row;
  justify-content: space-between;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex: 1;
`;

const HighlightedScoreText = styled(H3)`
  text-align: center;
`;

type SessionCardProps = {
  session: ConversationData;
  sessionNumber?: number;
};

const SessionCard: React.FC<SessionCardProps> = ({ session, sessionNumber }) => {
  const navigate = useNavigate();
  const { conversation_id, datetime, difficulty, product_selection, report, conversation_type } = session;
  const score = extractStringScore(report?.overall_score);

  const productConfig = useAppSelector(state => state.ecoach.productConfig);

  const productName = useMemo(() => {
    const productForCurrentCountry = productConfig?.[ECOACH_COUNTRY];
    console.log("productForCurrentCountry", productForCurrentCountry);
    return (
      productForCurrentCountry?.find((e) => e.product_code === product_selection)?.product_name ||
      product_selection
        ?.split("-")
        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );
  }, [product_selection, productConfig]);

  const onPress = () => {
    const productFlowType = mapConversationTypeToProductFlowType(conversation_type);
    navigate(`/report?conversationId=${conversation_id}&ref=history&productFlowType=${productFlowType}`, { state: { report: session } });
  };

  return (
    <Card onClick={onPress}>
      <HorizontalView>
        <Content>
          <H8 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
            {"Session"} {sessionNumber}
          </H8>
          <Label fontWeight="normal" color={colors.fwdDarkGreen[50]}>
            {moment(datetime).format("DD/MM")}
          </Label>
        </Content>
        <ChevronRightIcon fill={colors.fwdOrange[50]} size={18} />
      </HorizontalView>

      <HorizontalView>
        <Label fontWeight="bold" color={colors.fwdDarkGreen[50]} style={{ textAlign: "center" }}>
          <HighlightedScoreText fontWeight="bold" color={score < 60 ? colors.alertRed : colors.fwdDarkGreen[100]}>
            {score}{" "}
          </HighlightedScoreText>
          / 100
        </Label>
        <ContentView>
          <Content>
            <SmallLabel fontWeight="normal" color={colors.fwdDarkGreen[50]}>
              Script
            </SmallLabel>
            {productName && (
              <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[100]}>
                {productName}
              </SmallLabel>
            )}
          </Content>
          <Content>
            <SmallLabel fontWeight="normal" color={colors.fwdDarkGreen[50]}>
              Difficulty
            </SmallLabel>
            <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[100]}>
              {difficulty === "1" ? "Beginner" : "Expert"}
            </SmallLabel>
          </Content>
        </ContentView>
      </HorizontalView>
    </Card>
  );
};

export default React.memo(SessionCard);
