import { useCallback, useMemo, useState } from "react";
import styled from "styled-components";
import { colors } from "./CubeBaseElement";
import { useTranslation } from "react-i18next";

const ModalContainer = styled.div`
  position: fixed;
  background-color: #010101b2;
  backdrop-filter: blur(10px);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  display: grid;
  place-items: center;
`;
const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 28px;
  padding: 56px 20px;
  border-radius: 16px;
  color: white;
  position: relative;
  width: 90%;
  max-width: 600px;
  min-height: 414px;
  justify-content: center;
  align-items: center;
  & > * {
    text-align: center;
  }
  & > h2 {
    font-size: 48px;
    font-weight: bold;
    color: #f8f9f9;
  }
  & > .description {
    font-size: 20px;
    line-height: 1.5;
    color: #f8f9f9;
  }
`;
const ButtonsContainer = styled.div`
  display: flex;
  gap: 16px;
  padding: 0 16px;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  & > button {
    width: 100%;
    height: 48px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    background-color: #0078ce;
    color: white;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.5;
    &:active {
      background-color: ${colors.brilBlue[100]};
    }
  }
  & > button.secondary {
    background-color: ${colors.white};
    color: #0078ce;
    border: 1.5px solid #0078ce;
    &:active {
      background-color: ${colors.gray[200]};
    }
  }
`;
const Border = styled.div<{ confetti?: boolean }>`
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  border-radius: 16px;
  pointer-events: none;
  ${(props) =>
    props.confetti
      ? `
        &::before {
          content: "";
          position: absolute;
          inset: 0;
          border-radius: 16px;
          padding: 4px;
          background: linear-gradient(to bottom, #64BEFF, #0078CE);
          -webkit-mask:
            linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
        }
      `
      : `
        border: 4px solid #ffffff;
      `}
`;
const Dot = styled.div<{
  size?: number;
  confetti?: boolean;
  vertical: "top" | "bottom";
  horizontal: "left" | "right";
}>`
  position: absolute;
  width: ${(props) => props.size || 10}px;
  height: ${(props) => props.size || 10}px;
  background-color: ${(props) => (props.confetti ? "#E7F4FD" : "#ffffff")};
  border-radius: 50%;
  opacity: ${(props) => (props.confetti ? 1 : 0.5)};
  ${(props) =>
    props.vertical === "top"
      ? "top: 14px;"
      : props.vertical === "bottom"
      ? "bottom: 14px;"
      : ""};
  ${(props) =>
    props.horizontal === "left"
      ? "left: 14px;"
      : props.horizontal === "right"
      ? "right: 14px;"
      : ""};
`;
const Confetti = styled.div`
  position: absolute;
  top: -57px;
  left: 0;
  width: 100%;
  height: 115px;
  pointer-events: none;
  background: url("img/confetti.svg") no-repeat center center;
  background-size: cover;
`;

export type ModalData = {
  title: string;
  description: string;
  showConfetti?: boolean;
  primaryButton: {
    label: string;
    onClick: () => void;
  };
  secondaryButton?: {
    label: string;
    onClick: () => void;
  };
};

export function useExitModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [modalData, setModalData] = useState<ModalData | null>(null);
  const { t } = useTranslation("ecoach");

  const openModal = useCallback((data: ModalData) => {
    setModalData(data);
    setIsOpen(true);
  }, []);

  const getModalData = useCallback(
    (
      scenario: "completed" | "task-failed" | "early-quit",
      {
        primaryAction,
        secondaryAction,
      }: {
        primaryAction?: () => void;
        secondaryAction?: () => void;
      } = {}
    ): ModalData => {
      switch (scenario) {
        case "completed":
          return {
            title: t("sessionComplete"),
            description: t("thanksforPaticipating"),
            showConfetti: true,
            primaryButton: {
              label: t("viewReport"),
              onClick: () => {
                primaryAction?.();
              },
            },
            secondaryButton: {
              label: t("tryAgain"),
              onClick: () => {
                secondaryAction?.();
              },
            },
          };
        case "task-failed":
          return {
            title: t("missFail"),
            description: t("youHaveRanOutTime"),
            showConfetti: false,
            primaryButton: {
              label: t("viewReport"),
              onClick: () => {
                primaryAction?.();
              },
            },
            secondaryButton: {
              label: t("tryAgain"),
              onClick: () => {
                secondaryAction?.();
              },
            },
          };
        case "early-quit":
          return {
            title: t("quitEarlyTitle"),
            description: t("earlyQuitDescription"),
            showConfetti: false,
            primaryButton: {
              label: t("earlyQuitContinue"),
              onClick: () => {
                setIsOpen(false);
              },
            },
            secondaryButton: {
              label: t("exit"),
              onClick: () => {
                secondaryAction?.();
              },
            },
          };
        default:
          return {
            title: "",
            description: "",
            primaryButton: { label: "", onClick: () => {} },
          };
      }
    },
    [t]
  );

  const modalContent = useMemo(() => {
    if (!(isOpen && modalData)) {
      return <></>;
    }

    const { title, description, showConfetti, primaryButton, secondaryButton } =
      modalData;
    return (
      <>
        <ModalContainer onClick={() => setIsOpen(false)}>
          <ContentContainer>
            <Border confetti={showConfetti} />
            <Dot confetti={showConfetti} vertical="top" horizontal="left" />
            <Dot confetti={showConfetti} vertical="top" horizontal="right" />
            <Dot confetti={showConfetti} vertical="bottom" horizontal="left" />
            <Dot confetti={showConfetti} vertical="bottom" horizontal="right" />
            {showConfetti && <Confetti />}
            <h2>{title}</h2>
            <div className="description">{description}</div>
            <ButtonsContainer>
              <button
                onClick={() => {
                  primaryButton.onClick();
                  setIsOpen(false);
                }}
              >
                {primaryButton.label}
              </button>
              {secondaryButton && (
                <button
                  className="secondary"
                  onClick={() => {
                    secondaryButton.onClick();
                    setIsOpen(false);
                  }}
                >
                  {secondaryButton.label}
                </button>
              )}
            </ButtonsContainer>
          </ContentContainer>
        </ModalContainer>
      </>
    );
  }, [isOpen, modalData]);

  return {
    modalContent,
    openModal,
    getModalData,
  };
}
