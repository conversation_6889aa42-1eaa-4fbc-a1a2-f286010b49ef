import React from "react";
import { SvgIconProps } from "../../assets/icons/SvgIconProps";

const ChevronRightIcon = (props: SvgIconProps): JSX.Element => {
  return (
    <svg width={props.width || props.size} height={props.height || props.size} viewBox="0 0 24 24" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.3535 19.732L8 19.3785L15.293 12.0855L8 4.7925L8.3535 4.4395C8.939 3.8535 9.889 3.8535 10.475 4.4395L18.121 12.0855L10.475 19.732C9.889 20.3175 8.939 20.3175 8.3535 19.732Z"
        fill={props.fill}
      />
    </svg>
  );
};

export default ChevronRightIcon;
