import React from "react";
import styled from "styled-components";
import { colors, H6, LargeBody, sizes } from "../CubeBaseElement";
import TickCircleFillIcon from "../icons/TickCircleFillIcon";
import { useNavigate } from "react-router-dom";

type FeedbackSubmittedProps = {
  showGoHomeButton?: boolean;
};

const FeedbackSubmitted = ({ showGoHomeButton = true }: FeedbackSubmittedProps) => {
  const navigate = useNavigate();
  const returnToHome = () => {
    navigate("/");
  };

  return (
    <Container>
      <TickCircleFillIcon width={68} height={68} fill={colors.alertGreen} />

      <Content>
        <Title fontWeight="bold">Thank you</Title>
        <LargeBody style={{ textAlign: "center" }}>You have successfully submitted your feedback</LargeBody>
      </Content>

      {showGoHomeButton && (
        <ButtonContainer>
          <div onClick={returnToHome}>
            <Title fontWeight="bold">Back to home page</Title>
          </div>
        </ButtonContainer>
      )}
    </Container>
  );
};

const Container = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 241px;
  justify-content: center;
  align-items: center;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  padding: ${sizes[4]}px ${sizes[6]}px ${sizes[6]}px;
  width: 100%;
  align-items: center;
`;

const ButtonContainer = styled.div`
  gap: ${sizes[4]}px;
  width: 100%;
`;

const Title = styled(H6)`
  margin-bottom: ${sizes[2]}px;
`;

export { FeedbackSubmitted };
