import React, { useCallback, useEffect } from "react";
import styled from "styled-components";
import { colors, H6, H7, sizes } from "../CubeBaseElement";
import StarFillIcon from "../icons/StarFillIcon";
import StarIcon from "../icons/StarIcon";

type StarRatingProps = {
  title?: string;
  defaultValue?: number;
  value?: number;
  centerItem?: boolean;
  smallTitle?: boolean;
  readOnly?: boolean;
  onChange?: (value: number, event?: any) => void;
};

const STAR_SIZE = 33;
const NUMBER_OF_START = 5;

const StarRating: React.FC<StarRatingProps> = ({
  defaultValue = 0,
  value = undefined,
  title = "How was your experience?",
  onChange,
  centerItem = true,
  smallTitle = false,
  readOnly = false,
}) => {
  const [starValue, setValue] = React.useState(defaultValue);
  const stars = Array(NUMBER_OF_START).fill(0);

  useEffect(() => {
    if (value !== undefined) {
      setValue(value);
    }
  }, [value]);

  const onPress = useCallback(
    (v: number, oldValue: number, event: any) => {
      if (!readOnly) {
        setValue(v);
      }
      onChange?.(readOnly ? oldValue : v, event);
    },
    [onChange, readOnly]
  );

  return (
    <Container center={centerItem}>
      <TitleContainer>
        {smallTitle ? <H7 fontWeight="bold">{title}</H7> : <H6 fontWeight="bold">{title}</H6>}
      </TitleContainer>
      <ContentContainer>
        {stars.map((i, index) => (
          <Star key={`star_${index}`} onClick={(e) => onPress(index + 1, starValue, e)}>
            {starValue > index ? (
              <StarFillIcon size={STAR_SIZE} fill={colors.fwdOrange[100]} />
            ) : (
              <StarIcon size={STAR_SIZE} fill={colors.fwdOrange[100]} />
            )}
          </Star>
        ))}
      </ContentContainer>
    </Container>
  );
};

const Container = styled.div<{ center?: boolean }>`
  display: flex;
  gap: ${sizes[2]}px;
  align-items: ${({ center }) => (center ? "center" : "flex-start")};
  flex-direction: column;
`;

const TitleContainer = styled.div`
  display: flex;
`;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: ${sizes[2]}px;
`;

const Star = styled.div`
  cursor: pointer;
`;

export { StarRating };
