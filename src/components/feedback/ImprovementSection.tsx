import React, { useCallback, useEffect, useMemo } from "react";
import styled from "styled-components";
import { colors, H7, Label, sizes } from "../CubeBaseElement";
import VeryInterestedIcon from "../icons/VeryInterestedIcon";

type ImprovementSectionProps = {
  starRating: number;
  value: string | null;
  isLoading?: boolean;
  onChange?: (value: string, event?: any) => void;
};

const ImprovementSection: React.FC<ImprovementSectionProps> = ({ starRating, isLoading, value, onChange }) => {
  const [selected, setSelected] = React.useState<string | null>(value || "");

  useEffect(() => {
    setSelected(value);
  }, [value]);

  const feedbackReasons: { text: string; value: string }[] = useMemo(
    () => [
      { text: "Không chính xác", value: "inaccurate" },
      { text: "Không hữu ích", value: "unhelpful" },
      { text: "Chậm", value: "slow" },
      { text: "Khác", value: "other" },
    ],
    []
  );

  const onPress = useCallback(
    (v: string, event: any) => {
      setSelected(v);
      onChange?.(v, event);
    },
    [onChange]
  );

  return (
    <Container>
      <TitleContainer>
        <H7 fontWeight="bold" color={"#183028"}>
          {starRating <= 3 ? "Bạn không thích điều gì" : "Chúng tôi có thể cải thiện điều gì"}{" "}
        </H7>
        {starRating === 4 ? <VeryInterestedIcon width={20} fill={colors.fwdDarkGreen[100]} /> : null}
      </TitleContainer>
      <FeedbackReasonContainer>
        {feedbackReasons.map((option) => (
          <FeedbackReasonItem
            onClick={(e) => onPress(option.value, e)} // changed from onPress to onClick for web
            key={option.value}
            isSelected={selected === option.value}
            disabled={isLoading}
            style={{ opacity: isLoading ? 0.6 : 1 }}
          >
            <Label>{option.text}</Label>
          </FeedbackReasonItem>
        ))}
      </FeedbackReasonContainer>
    </Container>
  );
};

const Container = styled.div({
  display: "flex",
  gap: sizes[3],
  flexDirection: "column",
});

const TitleContainer = styled.div({
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  height: sizes[6],
});

const FeedbackReasonContainer = styled.div({
  display: "flex",
  gap: sizes[1],
  flexWrap: "wrap",
  flexDirection: "row",
  marginTop: sizes[3],
});

const FeedbackReasonItem = styled.button<{ isSelected: boolean }>`
  display: flex;
  font-weight: ${({ isSelected }) => (isSelected ? "500" : "450")};
  border: ${({ isSelected }) =>
    isSelected ? `2px solid ${colors.fwdOrange[100]}` : `1px solid ${colors.fwdGrey[100]}`};
  background-color: ${({ isSelected }) => (isSelected ? colors.fwdOrange[5] : colors.white)};
  padding: ${sizes[2]}px ${sizes[3]}px;
  border-radius: 30px;
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ isSelected }) => (isSelected ? colors.fwdOrange[5] : colors.fwdGrey[20])}; /* Hover effect */
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

export { ImprovementSection };
