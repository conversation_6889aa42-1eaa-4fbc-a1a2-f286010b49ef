import React from "react";
import { useNavigate } from "react-router-dom";
import { girlListenMusic } from "../assets";
import { colors, H6, sizes } from "./CubeBaseElement";
import styled from "styled-components";
import ModalFullPage from "@components/modal-full-page";
import { useTranslation } from "react-i18next";

const CenterView = styled.div`
  justify-content: center;
  height: 261px;
  border-radius: ${sizes[4]}px;
  padding: ${sizes[6]}px;
  background-color: ${colors.white};
  align-items: center;
  display: flex;
  flex-direction: column;
`;

const Container = styled.div`
  display: flex;
  background-color: #fff;
  justify-content: center;
  align-items: center;
  flex: 1;
  height: calc(100vh - 56px);
  width: 100%;
`;

const GirlImage = styled.img`
  width: 145px;
  height: 138px;
`;

const NotReadyText = styled(H6)`
  color: ${colors.fwdOrange[100]};
  text-align: center;
  margin-top: 1rem;
`;

const AvatarNotReady = () => {
  const navigate = useNavigate();
  const { t } = useTranslation("ecoach");

  const onDismiss = () => {
    navigate("/");
  };

  return (
    <ModalFullPage show={true} title="" onClose={onDismiss}>
      <Container onClick={onDismiss}>
        <CenterView>
          <GirlImage src={girlListenMusic} alt={"avatar"} />
          <NotReadyText fontWeight={"bold"} color={colors.fwdOrange[100]}>
            {t("avatarNotReadyNotification")}
          </NotReadyText>
        </CenterView>
      </Container>
    </ModalFullPage>
  );
};

export default AvatarNotReady;
