import styled, { css } from "styled-components";

interface ButtonProps {
  variant?: "primary" | "secondary" | "outline";
  size?: "small" | "medium" | "large";
  fullWidth?: boolean;
  disabled?: boolean;
}

const buttonVariants = {
  primary: css`
    background-color: ${({ theme }) => theme.color.text.primary};
    color: ${({ theme }) => theme.color.status.white};
    border: 2px solid ${({ theme }) => theme.color.text.primary};

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.color.status.primary_30};
      border-color: ${({ theme }) => theme.color.status.primary_30};
    }
  `,
  secondary: css`
    background-color: ${({ theme }) => theme.color.status.grey_20};
    color: ${({ theme }) => theme.color.text.body};
    border: 2px solid ${({ theme }) => theme.color.status.grey};

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.color.status.grey_50};
    }
  `,
  outline: css`
    background-color: transparent;
    color: ${({ theme }) => theme.color.text.primary};
    border: 2px solid ${({ theme }) => theme.color.text.primary};

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.color.text.primary};
      color: ${({ theme }) => theme.color.status.white};
    }
  `,
};

const buttonSizes = {
  small: css`
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 6px;
  `,
  medium: css`
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
  `,
  large: css`
    padding: 16px 32px;
    font-size: 18px;
    border-radius: 10px;
  `,
};

export const Button = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border: none;
  outline: none;

  ${({ variant = "primary" }) => buttonVariants[variant]}
  ${({ size = "medium" }) => buttonSizes[size]}

  ${({ fullWidth }) =>
    fullWidth &&
    css`
      width: 100%;
    `}

  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    `}

  &:focus {
    box-shadow: 0 0 0 3px rgba(232, 119, 34, 0.2);
  }
`;

export const ButtonPrimary = styled(Button).attrs({ variant: "primary" })``;
export const ButtonSecondary = styled(Button).attrs({ variant: "secondary" })``;
export const ButtonOutline = styled(Button).attrs({ variant: "outline" })``;

export const ButtonIcon = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease-in-out;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(232, 119, 34, 0.2);
  }
`;
