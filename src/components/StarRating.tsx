import React from 'react';
import styled from 'styled-components';
import { colors } from './CubeBaseElement';

interface StarRatingProps {
  overallScore: number;
  size?: number;
  fill?: string;
}

const StarContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
`;


// Simple star components using SVG
const Star = ({ size = 28 }: { size?: number; fill?: string }) => (
<svg  width={size} height={size} viewBox="0 0 24 24" fill="none">
  <path
    d="M17.4225 21.116C17.2655 21.116 17.1075 21.079 16.9625 21.004L12.1045 18.489L7.24646 21.004C6.90946 21.178 6.50196 21.147 6.19546 20.922C5.88896 20.6975 5.73646 20.3185 5.80146 19.9445L6.72596 14.638L2.80796 10.877C2.53196 10.612 2.43096 10.213 2.54796 9.8495C2.66546 9.485 2.97996 9.22 3.35846 9.166L8.78596 8.389L11.2105 3.5515C11.3805 3.2135 11.726 3 12.1045 3C12.483 3 12.829 3.2135 12.9985 3.5515L15.423 8.389L20.85 9.166C21.2285 9.22 21.543 9.485 21.6605 9.849C21.7775 10.213 21.677 10.612 21.401 10.8765L17.4835 14.638L18.408 19.9445C18.473 20.3185 18.3205 20.6975 18.014 20.922C17.839 21.0505 17.6315 21.116 17.4225 21.116Z"
    stroke="#FF9B0A"
    strokeWidth="1"
    fill="none"
  />
</svg>
);

const StarHalf = ({ size = 28, fill = '#ffd700' }: { size?: number; fill?: string }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <mask id="path-1-inside-1_271_6934" fill="white">
      <path fillRule="evenodd" clipRule="evenodd" d="M17.4225 21.116C17.2655 21.116 17.1075 21.079 16.9625 21.004L12.1045 18.489L7.24646 21.004C6.90946 21.178 6.50196 21.147 6.19546 20.922C5.88896 20.6975 5.73646 20.3185 5.80146 19.9445L6.72596 14.638L2.80796 10.877C2.53196 10.612 2.43096 10.213 2.54796 9.8495C2.66546 9.485 2.97996 9.22 3.35846 9.166L8.78596 8.389L11.2105 3.5515C11.3805 3.2135 11.726 3 12.1045 3C12.483 3 12.829 3.2135 12.9985 3.5515L15.423 8.389L20.85 9.166C21.2285 9.22 21.543 9.485 21.6605 9.849C21.7775 10.213 21.677 10.612 21.401 10.8765L17.4835 14.638L18.408 19.9445C18.473 20.3185 18.3205 20.6975 18.014 20.922C17.839 21.0505 17.6315 21.116 17.4225 21.116Z"/>
    </mask>
    <path d="M16.9625 21.004L16.5027 21.8921L16.503 21.8922L16.9625 21.004ZM12.1045 18.489L12.5642 17.601L12.1045 17.3629L11.6447 17.601L12.1045 18.489ZM7.24646 21.004L7.70524 21.8926L7.70621 21.8921L7.24646 21.004ZM6.19546 20.922L6.78722 20.1159L6.78637 20.1153L6.19546 20.922ZM5.80146 19.9445L4.8163 19.7729L4.81623 19.7733L5.80146 19.9445ZM6.72596 14.638L7.71112 14.8096L7.80249 14.2852L7.41847 13.9166L6.72596 14.638ZM2.80796 10.877L2.11537 11.5983L2.11546 11.5984L2.80796 10.877ZM2.54796 9.8495L1.59619 9.54269L1.59606 9.54311L2.54796 9.8495ZM3.35846 9.166L3.4997 10.156L3.50018 10.1559L3.35846 9.166ZM8.78596 8.389L8.92768 9.37891L9.44555 9.30477L9.67996 8.83706L8.78596 8.389ZM11.2105 3.5515L10.3171 3.10217L10.3165 3.10344L11.2105 3.5515ZM12.9985 3.5515L13.8925 3.10344L13.8924 3.10323L12.9985 3.5515ZM15.423 8.389L14.529 8.83706L14.7634 9.30476L15.2812 9.37891L15.423 8.389ZM20.85 9.166L20.7082 10.1559L20.7087 10.156L20.85 9.166ZM21.6605 9.849L22.6125 9.54299L22.6121 9.54181L21.6605 9.849ZM21.401 10.8765L20.7091 10.1545L20.7084 10.1552L21.401 10.8765ZM17.4835 14.638L16.7909 13.9167L16.407 14.2853L16.4983 14.8096L17.4835 14.638ZM18.408 19.9445L19.3932 19.7733L19.3931 19.7729L18.408 19.9445ZM18.014 20.922L17.4231 20.1153L17.4221 20.116L18.014 20.922ZM17.4225 21.116V20.116L17.4219 20.1158L16.9625 21.004L16.503 21.8922C16.7926 22.042 17.1085 22.116 17.4225 22.116V21.116ZM16.9625 21.004L17.4222 20.116L12.5642 17.601L12.1045 18.489L11.6447 19.3771L16.5027 21.8921L16.9625 21.004ZM12.1045 18.489L11.6447 17.601L6.78672 20.116L7.24646 21.004L7.70621 21.8921L12.5642 19.3771L12.1045 18.489ZM7.24646 21.004L6.78768 20.1154L6.78722 20.1159L6.19546 20.922L5.6037 21.7281C6.21757 22.1788 7.03219 22.2401 7.70524 21.8926L7.24646 21.004ZM6.19546 20.922L6.78637 20.1153L6.78669 20.1157L5.80146 19.9445L4.81623 19.7733C4.68623 20.5213 4.99109 21.2794 5.60455 21.7287L6.19546 20.922ZM5.80146 19.9445L6.78662 20.1161L7.71112 14.8096L6.72596 14.638L5.7408 14.4664L4.8163 19.7729L5.80146 19.9445ZM6.72596 14.638L7.41847 13.9166L3.50047 10.1556L2.80796 10.877L2.11546 11.5984L6.03346 15.3594L6.72596 14.638ZM2.80796 10.877L3.50055 10.1557L3.49987 10.1559L2.54796 9.8495L1.59606 9.54311C1.36156 10.2716 1.56435 11.0693 2.11537 11.5983L2.80796 10.877ZM2.54796 9.8495L3.49973 10.1563L3.4997 10.156L3.35846 9.166L3.21722 8.17603C2.45991 8.28407 1.83095 8.81444 1.59619 9.54269L2.54796 9.8495ZM3.35846 9.166L3.50018 10.1559L8.92768 9.37891L8.78596 8.389L8.64425 7.39909L3.21675 8.17609L3.35846 9.166ZM8.78596 8.389L9.67996 8.83706L12.1045 3.99956L11.2105 3.5515L10.3165 3.10344L7.89196 7.94094L8.78596 8.389ZM11.2105 3.5515L12.1038 4.00083C12.1042 4.00013 12.1043 4.00011 12.1045 3.99997C12.1047 3.99986 12.1043 4 12.1045 4V3V2C11.3472 2 10.6565 2.42735 10.3171 3.10217L11.2105 3.5515ZM12.1045 3V4L12.1046 3.99977L12.9985 3.5515L13.8924 3.10323C13.5531 2.42674 12.861 2 12.1045 2V3ZM12.9985 3.5515L12.1045 3.99956L14.529 8.83706L15.423 8.389L16.317 7.94094L13.8925 3.10344L12.9985 3.5515ZM15.423 8.389L15.2812 9.37891L20.7082 10.1559L20.85 9.166L20.9917 8.1761L15.5647 7.39909L15.423 8.389ZM20.85 9.166L20.7087 10.156L20.7088 10.1562L21.6605 9.849L22.6121 9.54181C22.3772 8.81416 21.7483 8.28404 20.9912 8.17603L20.85 9.166ZM21.6605 9.849L20.7084 10.155L20.7091 10.1545L21.401 10.8765L22.0929 11.5985C22.6458 11.0686 22.8463 10.2703 22.6125 9.54299L21.6605 9.849ZM21.401 10.8765L20.7084 10.1552L16.7909 13.9167L17.4835 14.638L18.1761 15.3593L22.0936 11.5978L21.401 10.8765ZM17.4835 14.638L16.4983 14.8096L17.4228 20.1161L18.408 19.9445L19.3931 19.7729L18.4686 14.4664L17.4835 14.638ZM18.408 19.9445L17.4227 20.1157L17.4231 20.1153L18.014 20.922L18.6049 21.7287C19.2183 21.2794 19.5232 20.5213 19.3932 19.7733L18.408 19.9445ZM18.014 20.922L17.4221 20.116L17.4225 20.116V21.116V22.116C17.8406 22.116 18.2562 21.9847 18.6058 21.728L18.014 20.922Z" fill={fill} mask="url(#path-1-inside-1_271_6934)"/>
    <path d="M12.1006 18.4922L7.24713 21.0049C6.91013 21.1789 6.50187 21.1479 6.19537 20.9229C5.88903 20.6983 5.73683 20.3192 5.80181 19.9453L6.72662 14.6387L2.80767 10.8779C2.53192 10.613 2.43104 10.2139 2.54791 9.85059C2.66539 9.48615 2.98004 9.22106 3.35845 9.16699L8.78619 8.38965L11.211 3.55273C11.3804 3.21593 11.7237 3.00346 12.1006 3.00195V18.4922Z" fill={fill}/>
  </svg>
);

const StarFill = ({ size = 28, fill = '#ffd700' }: { size?: number; fill?: string }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.4225 21.116C17.2655 21.116 17.1075 21.079 16.9625 21.004L12.1045 18.489L7.24646 21.004C6.90946 21.178 6.50196 21.147 6.19546 20.922C5.88896 20.6975 5.73646 20.3185 5.80146 19.9445L6.72596 14.638L2.80796 10.877C2.53196 10.612 2.43096 10.213 2.54796 9.8495C2.66546 9.485 2.97996 9.22 3.35846 9.166L8.78596 8.389L11.2105 3.5515C11.3805 3.2135 11.726 3 12.1045 3C12.483 3 12.829 3.2135 12.9985 3.5515L15.423 8.389L20.85 9.166C21.2285 9.22 21.543 9.485 21.6605 9.849C21.7775 10.213 21.677 10.612 21.401 10.8765L17.4835 14.638L18.408 19.9445C18.473 20.3185 18.3205 20.6975 18.014 20.922C17.839 21.0505 17.6315 21.116 17.4225 21.116Z" fill={fill}/>
  </svg>
);

const StarRating: React.FC<StarRatingProps> = ({
  overallScore, 
  size = 24,
  fill = colors.fwdAlternativeOrange[20]
}) => {
  const renderStars = () => {
    if (overallScore < 14) {
      return (
        <>
          <Star size={size} />
          <Star size={size} />
          <Star size={size} />
        </>
      );
    } else if (overallScore >= 14 && overallScore < 28) {
      return (
        <>
          <StarHalf fill={fill} size={size} />
          <Star size={size} />
          <Star size={size} />
        </>
      );
    } else if (overallScore >= 28 && overallScore < 42) {
      return (
        <>
          <StarFill fill={fill} size={size} />
          <Star size={size} />
          <Star size={size} />
        </>
      );
    } else if (overallScore >= 42 && overallScore < 56) {
      return (
        <>
          <StarFill fill={fill} size={size} />
          <StarHalf fill={fill} size={size} />
          <Star size={size} />
        </>
      );
    } else if (overallScore >= 56 && overallScore < 70) {
      return (
        <>
          <StarFill fill={fill} size={size} />
          <StarFill fill={fill} size={size} />
          <Star size={size} />
        </>
      );
    } else if (overallScore >= 70 && overallScore < 84) {
      return (
        <>
          <StarFill fill={fill} size={size} />
          <StarFill fill={fill} size={size} />
          <StarHalf fill={fill} size={size} />
        </>
      );
    } else if (overallScore >= 84) {
      return (
        <>
          <StarFill size={size} fill={fill} />
          <StarFill size={size} fill={fill} />
          <StarFill size={size} fill={fill} />
        </>
      );
    }
    
    return (
      <>
        <Star size={size} />
        <Star size={size} />
        <Star size={size} />
      </>
    );
  };

  return (
    <StarContainer>
      {renderStars()}
    </StarContainer>
  );
};

export default StarRating; 