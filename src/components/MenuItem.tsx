import React, { useState } from 'react';
import styled from 'styled-components';
import { H7, SmallBody, LargeBody, colors, sizes } from './CubeBaseElement';
import { useTranslation } from 'react-i18next';
import { doubleQuoteMark } from "@/assets";
// Simple chevron icons
const ChevronDown = ({ size, color }: { size: number; color: string }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <path d="M6 9l6 6 6-6" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const ChevronUp = ({ size, color }: { size: number; color: string }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <path d="M18 15l-6-6-6 6" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

interface MenuItemProps {
  index: number;
  active: boolean;
  title: string;
  observation: string;
  insight: string;
  action: string;
}

const ExpandedView = styled.div`
  margin: 8px 0;
  gap: ${sizes[4]}px;
  display: flex;
  flex-direction: column;
`;

const Container = styled.div`
  margin-top: 8px;
  margin-bottom: 8px;
`;


const ItemHeader = styled.div<{ $expanded: boolean; $active: boolean }>`
  display: flex;
  flex-direction: row;
  background-color: ${({ $expanded, $active }) => 
    $expanded 
      ? $active 
        ? "rgba(154, 213, 255, 0.80)"
        : colors.fwdOrange[20]
      : 'transparent'
  };
  align-items: center;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: ${sizes[1]}px ${sizes[3]}px;
  cursor: pointer;
`;

const ItemNumber = styled.div<{ $expanded: boolean; $active: boolean }>`
  background-color: ${({ $expanded, $active }) =>
      $active
          ? $expanded
              ? "#E2F3FF" : "rgba(154, 213, 255, 0.50)"
          :  colors.alertRedLight
  };
  width: 22px;
  height: 31px;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: ${sizes[2]}px;
`;

const ItemNumberText = styled(H7)<{ $active: boolean }>`
  color: ${({ $active }) => $active ? colors.alertGreen : colors.alertRed};
`;

const ItemTitle = styled(H7)`
  color: ${colors.fwdDarkGreen[100]};
  flex: 1;
`;

const HighLightItem = styled.div`
  background-color: ${colors.fwdOrange[20]};
  padding: ${sizes[4]}px;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
`;

const ItemView = styled.div`
  display: flex;
  flex-direction: column;
`;

const InsideText = styled(LargeBody)`
  color: ${colors.fwdDarkGreen[100]};
`;

const ObservationText = styled(SmallBody)`
  color: ${colors.fwdDarkGreen[100]};
`;

const ObservationView = styled.div`
  border-left: 1px solid ${colors.fwdGrey[100]};
  padding: 0 ${sizes[3]}px;
`;

const HighlightText = styled(SmallBody)`
  color: ${colors.fwdDarkGreen[100]};
  max-width: 90%;
`;

const Divider = styled.div`
  height: 1px;
  background-color: ${colors.fwdGrey[100]};
`;

const DoubleQuoteMarkContainer = styled.div(() => ({
  flexDirection: 'row',
  alignSelf: 'flex-start',
}));

const DoubleQuoteMarkImage = styled.img(() => ({
  width: 36,
  height: 28,
}));


const MenuItem: React.FC<MenuItemProps> = ({
  active,
  index,
  title,
  observation,
  insight,
  action,
}) => {
  const [expanded, setExpanded] = useState(false);
  const { t } = useTranslation("ecoach");
  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  return (
    <Container>
      <ItemHeader $expanded={expanded} $active={active} onClick={toggleExpand}>
        <ItemNumber $expanded={expanded} $active={active}>
          <ItemNumberText fontWeight="bold" $active={active}>
            {index + 1}
          </ItemNumberText>
        </ItemNumber>
        <ItemTitle fontWeight="bold">{title}</ItemTitle>
        {expanded ? (
          <ChevronUp size={20} color={colors.fwdDarkGreen[100]} />
        ) : (
          <ChevronDown size={20} color={colors.fwdDarkGreen[100]} />
        )}
      </ItemHeader>

      {expanded && (
        <ExpandedView>
          <InsideText>{insight}</InsideText>
          <ObservationView>
            <ObservationText>"{observation}"</ObservationText>
          </ObservationView>
          {!active && (
            <HighLightItem>
              <ItemView >
                <ItemTitle fontWeight="bold">
                  {t('suggestedNextMove')}
                </ItemTitle>
                <HighlightText>{action}</HighlightText>
              </ItemView>
              <DoubleQuoteMarkContainer>
                <DoubleQuoteMarkImage src={doubleQuoteMark}  />
              </DoubleQuoteMarkContainer>
            </HighLightItem>
          )}
        </ExpandedView>
      )}
      <Divider />
    </Container>
  );
};

export default MenuItem; 