import { useEffect, useRef, useState, type FC } from "react";
import useWindowResize from "@hooks/use-window-resize";
import styled from "styled-components";

import {
  Config,
  PixelStreaming,
} from "@epicgames-ps/lib-pixelstreamingfrontend-ue5.4";
import useMyActivity from "@/hooks/use-my-activity";

export type VideoStreamingV2Props = {
  conversationId: string;
  isFocused: boolean;
  avatarId: string;
  isTimeOut: boolean;
  style?: any;
  onVideoReady?: () => void;
};

export const VideoStreamingV2: FC<VideoStreamingV2Props> = ({
  conversationId,
  isFocused,
  avatarId,
  isTimeOut,
  style,
  onVideoReady,
}) => {
  const { accessToken } = useMyActivity();
  const { width = 0, height = 0 } = useWindowResize();
  const frameWidth = width < 750 ? Math.round((height * 16) / 9) : width;
  // A reference to parent div element that the Pixel Streaming library attaches into:
  const videoParent = useRef<HTMLDivElement>(null);
  const pixelStreamingRef = useRef<PixelStreaming | null>(null);
  // Pixel streaming library instance is stored into this state variable after initialization:
  const [pixelStreaming, setPixelStreaming] = useState<PixelStreaming>();
  // A boolean state variable that determines if the Click to play overlay is shown:
  const [clickToPlayVisible, setClickToPlayVisible] = useState(false);

  // Run on component mount:
  useEffect(() => {
    if (videoParent.current) {
      // Attach Pixel Streaming library to videoParent element:
      const url = `${import.meta.env.VITE_ECOACH_PUBLIC_AVATAR_WEBSOCKET_URL}${
        avatarId ? `/${avatarId}` : ""
      }?access_token=${accessToken}&country=bril`;
      const config = new Config({
        initialSettings: {
          AutoPlayVideo: true,
          AutoConnect: true,
          ss: url,
          StartVideoMuted: true,
          HoveringMouse: true,
          WaitForStreamer: true,
        },
      });
      const streaming = new PixelStreaming(config, {
        videoElementParent: videoParent.current,
      });

      // register a playStreamRejected handler to show Click to play overlay if needed:
      streaming.addEventListener("playStreamRejected", () => {
        setClickToPlayVisible(true);
      });

      streaming.addEventListener("playStream", () => {
        onVideoReady?.();
      });

      // Save the library instance into component state so that it can be accessed later:
      setPixelStreaming(streaming);
      pixelStreamingRef.current = streaming;

      // Clean up on component unmount:
      return () => {
        try {
          streaming.disconnect();
        } catch {}
      };
    }
  }, []);

  useEffect(() => {
    if (isTimeOut || !isFocused) {
      pixelStreamingRef.current?.disconnect();
    }
  }, [isTimeOut, isFocused]);

  return (
    <Container
      style={
        style || width < 750
          ? {
              width: frameWidth,
              transform: `translate(-${frameWidth / 2 - width / 2}px, 0)`,
            }
          : {}
      }
    >
      <div
        style={{
          width: "100%",
          height: "100%",
        }}
        ref={videoParent}
      />
      {clickToPlayVisible && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
          }}
          onClick={() => {
            pixelStreaming?.play();
            setClickToPlayVisible(false);
          }}
        >
          <div>Click to play</div>
        </div>
      )}
    </Container>
  );
};

const Container = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  flex: 1;
  width: 100%;
  height: 100%;
  background-color: black;
`;
