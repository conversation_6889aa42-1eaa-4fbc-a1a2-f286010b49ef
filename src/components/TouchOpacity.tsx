import styled from "styled-components";
import { colors } from "./CubeBaseElement";

const TouchOpacity = styled.div`
  /* position: relative;
  display: inline-block;
  cursor: pointer;
  user-select: none;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  transition: opacity 0.2s ease-in-out; */
  &:active {
    opacity: 0.6;
  }
  /* &:focus {
    outline: none;
  }
  &:focus-visible {
    outline: 2px solid ${colors.brilBlue[100]};
    outline-offset: 2px;
  }
  & > * {
    flex-shrink: 0;
  } */
`;

export default TouchOpacity;