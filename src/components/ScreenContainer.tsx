import styled from "styled-components";
import { loungeBri } from "../assets";
import { colors, sizes } from "./CubeBaseElement";
import { INSETS } from "@/utils/insets";
import { FC } from "react";
import TouchOpacity from "./TouchOpacity";
import { ChevronRight } from "./icons";

export const DarkBackgroundOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(180deg, rgba(1, 1, 1, 0.5) 0%, #000 50%);
  z-index: 999;
`;

export const BackgroundImageOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background-image: url(${loungeBri});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
`;

export const ScreenContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  padding: 0 ${sizes[4]}px;
  padding-top: ${INSETS.top}px;
  overflow-y: hidden;
  min-height: calc(100vh - ${sizes[10]}px);
  max-height: 100dvh;
  background-image: linear-gradient(
    180deg,
    rgba(1, 1, 1, 0.5) 5.61%,
    rgba(1, 1, 1, 0) 18.79%,
    #010101 56.31%
  );
  color: ${colors.white};
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;

  & > * {
    flex-shrink: 0;
  }
`;

export const ScreenHeader = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
  z-index: 1001;
  margin-bottom: ${sizes[4]}px;
`;

export const ScreenContent = styled.div`
  flex: 1;
  overflow-y: auto;
  /* padding: 0 ${sizes[4]}px;
  margin: 0 ${-sizes[4]}px; */
`;

export const ScreenFooter = styled.div`
  margin-bottom: ${INSETS.bottom}px;
  margin-top: ${sizes[4]}px;
`;

const StyledBackButton = styled(TouchOpacity)`
  margin-left: -${sizes[4]}px;
  padding: ${sizes[2]}px;
  height: 45px;
  width: 45px;
  & svg {
    transform: rotate(180deg);
  }
`;

export const BackButton: FC<{ onClick: () => void }> = ({ onClick }) => (
  <StyledBackButton onClick={onClick}>
    <ChevronRight />
  </StyledBackButton>
);
