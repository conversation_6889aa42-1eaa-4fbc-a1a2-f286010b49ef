import React, { useRef, useEffect } from "react";
import type { FC, ReactNode, ReactElement } from "react";
import styled, { css } from "styled-components";
import { colors } from "./CubeBaseElement";

const ACTIVE_COLOR = colors.brilBlue[100];

export type TabsProps = {
  activeIndex?: number;
  onChange?: (index: number) => void;
  children?: ReactElement<TabItemProps>[] | ReactElement<TabItemProps>;
};

export const Tabs: FC<TabsProps> = ({ activeIndex, onChange, children }) => {
  const tabRefs = useRef<Array<HTMLDivElement | null>>([]);

  // Scroll active tab into view when activeIndex changes
  useEffect(() => {
    if (
      typeof activeIndex === "number" &&
      tabRefs.current[activeIndex]
    ) {
      const tab = tabRefs.current[activeIndex];
      const tabContainer = tab?.parentElement;
      if (tab && tabContainer) {
        const tabRect = tab.getBoundingClientRect();
        const containerRect = tabContainer.getBoundingClientRect();
        // Only scroll if tab is not fully visible horizontally
        if (tabRect.left < containerRect.left || tabRect.right > containerRect.right) {
          tab.scrollIntoView({
            behavior: "smooth",
            inline: "center",
            block: "nearest", // Prevent vertical scroll
          });
        }
      }
    }
  }, [activeIndex]);

  // Add index to each TabItem child and ref
  const items = React.Children.map(children, (child, idx) => {
    if (!React.isValidElement(child)) return child;
    return React.cloneElement(child, {
      active: activeIndex === idx,
      onClick: () => onChange?.(idx),
      ref: (el: HTMLDivElement) => (tabRefs.current[idx] = el),
    });
  });
  return <TabContainer>{items}</TabContainer>;
};

export type TabItemProps = {
  children?: ReactNode;
  active?: boolean;
  onClick?: () => void;
  ref?: React.Ref<HTMLDivElement>;
};

export const TabItem = React.forwardRef<HTMLDivElement, TabItemProps>(
  ({ children, active, onClick }, ref) => (
    <TabItemContainer $active={active} onClick={onClick} ref={ref}>
      {children}
    </TabItemContainer>
  )
);

const TabContainer = styled.div`
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #4d4d4d;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  /* Hide the scrollbar */
  &::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
`;

const TabItemContainer = styled.div<{ $active?: boolean }>`
  padding: 10px 8px;
  cursor: pointer;
  font-size: 14px;
  color: #f8f9f9;
  margin-bottom: -1px;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  transition: border-bottom 0.2s, color 0.2s;

  ${({ $active }) =>
    $active &&
    css`
      color: ${ACTIVE_COLOR};
      border-bottom: 3px solid ${ACTIVE_COLOR};
      font-weight: bold;
    `}

  /* No padding left for first item */
  &:first-child {
    padding-left: 0;
  }
`;
