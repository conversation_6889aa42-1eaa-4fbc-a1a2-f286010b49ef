import { ConversationType } from "../types/ecoach-custom";

/**
 * extract score 0 from "0.00 / 100"
 * @param scoreString
 */
export const extractStringScore = (scoreString?: string) => {
  if (!scoreString) return 0;
  try {
    const extractedString = scoreString.split(" / ")[0];
    const extractedNumber = parseFloat(extractedString);
    return Math.floor(extractedNumber);
  } catch (error) {
    return 0;
  }
};


/**
 * extract score from report "
 */
export const extractScore = (conversation_type: string | any, report: Report | any, score: string | any) => {
  if (conversation_type === ConversationType.FULL_EXPERIENCE) {
    const scoreString = report?.overall_score;
    const extractedString = scoreString.split(" / ")[0];
    const extractedNumber = parseFloat(extractedString);
    return Math.floor(extractedNumber);
  }
  if (conversation_type === ConversationType.QUICKFIRE) {
    const answered_length = report?.questions_and_assessments?.length;
    const correct = report?.questions_and_assessments.filter((item: {
      answered_correctly: string;
    }) => item.answered_correctly === "Yes");
    const score = answered_length > 0 ? Math.round((correct.length / answered_length) * 100) : 0;
    return score.toString();
  }
  if (conversation_type === ConversationType.OBJECTION_HANDLING) {
    return score ? Math.floor(score) : 0;
  }
  return 0;
};