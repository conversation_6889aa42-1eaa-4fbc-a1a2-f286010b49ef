import { ProductFlowType } from "../@custom-types/ecoach";
import { ConversationType } from "../types/ecoach-custom";

/**
 * Maps conversation_type to the corresponding productFlowType
 * @param conversationType The conversation type from session data
 * @returns The corresponding ProductFlowType for URL parameters
 */
export const mapConversationTypeToProductFlowType = (
  conversationType?: ConversationType
): ProductFlowType => {
  switch (conversationType) {
    case ConversationType.QUICKFIRE:
      return ProductFlowType.QUICKFIRE;
    case ConversationType.OBJECTION_HANDLING:
      return ProductFlowType.OBJECTION_HANDLING;
    case ConversationType.FULL_EXPERIENCE:
    default:
      return ProductFlowType.FULL_EXPERIENCE;
  }
};
