export default {
  reqPermission: "<PERSON><PERSON> me<PERSON> izin Anda untuk menampilkan kamera",
  grandPermission: "Berikan izin",
  youFail: "Anda <PERSON>",
  ooHeart: "<PERSON>i Anda telah habis!",
  exitSS: "<PERSON><PERSON><PERSON>",
  reTake: "<PERSON> ulangi",
  leaving: " sedang pergi!",
  minuteLeft: `Anda memiliki 1 menit tersisa!`,
  secondsLeft: `Anda memiliki 30 detik tersisa!`,
  wrapUp: `<PERSON> a<PERSON> perca<PERSON>pan`,
  points: " poin",
  saleCoPilot: "Co-Pilot Penjualan",
  trainMaterials: "<PERSON><PERSON> Pelatihan",
  yourSS: "<PERSON><PERSON> Anda",
  selectProduct: "Pilih produk FWD:",
  selectPolicy: "Skenario",
  selectDifficulty: "Tingkat Kesulitan",
  practiceText:
    "Berlatih cara unggul dalam pembicaraan penjualan, melalui skenario virtual yang akan menguji dan meningkatkan keterampilan Anda!",
  letGo: "Ayo mulai!",
  home: "<PERSON><PERSON><PERSON>",
  yourSummary: "<PERSON>kas<PERSON>",
  hereIsSummary: "<PERSON><PERSON><PERSON> adalah ringkasan dari",
  ss: "sesi",
  retrySS: "Ulangi Sesi",
  genFullReport: "Buat Laporan Lengkap",
  hello: "Halo,",
  getToKnowBetter: "Mari mengenal Anda lebih baik!",
  getStarted: "Mari Mulai!",
  yearsOfExperience: "Tahun pengalaman sebagai agen",
  experienceLevelText: "Tingkat Pengalaman",
  whatWouldYouLikeToLearn: "Apa yang ingin Anda pelajari?",
  threeMonths: "0-3 bulan",
  twelveMonths: "3-12 bulan",
  twoYears: "1-2 tahun",
  threePlusYears: "3+ tahun",
  newToFWD: "Saya baru di FWD!",
  trainee: "Peserta Pelatihan",
  agent: "Agen",
  communicationSkills: "Keterampilan komunikasi",
  analyticalSkills: "Keterampilan Analitis",
  customerRelationship: "Hubungan Pelanggan",
  salesSkills: "Keterampilan Penjualan",
  improvePerformance: "Tingkatkan Kinerja",
  wellDone: "Kerja Bagus!",
  congratsText: "Selamat telah menyelesaikan",
  howDoUThink: "Bagaimana menurut Anda?",
  confidenceQuestion:
    "Seberapa banyak latihan ini membantu Anda dengan pengetahuan produk?",
  helpSellQuestion:
    "Seberapa banyak latihan ini membantu membangun kepercayaan diri Anda untuk bertemu klien sungguhan?",
  letsSeeResult: "Mari lihat hasil saya!",
  isThinking: "sedang berpikir ...",
  next: "Selanjutnya",
  cusProfile: "Profil pelanggan Anda",
  lifeSetDes:
    "Jual rencana ini kepada pelanggan dengan kebutuhan perlindungan pendapatan",
  healthSetDes:
    "Jual rencana ini kepada pelanggan dengan kebutuhan perlindungan kesehatan",
  beginnerDes:
    "Apakah Anda baru atau perlu penyegaran dasar-dasar? Mulai di sini!",
  expertDes:
    "Sudah cukup berpengalaman? Coba level ini untuk meningkatkan permainan Anda!",
  introText:
    "Pelanggan Virtual Anda sedang bersiap, Sementara itu, mari mengenalnya!",
  basic: "Dasar",
  language: "Bahasa",
  moreAbout: "Lebih lanjut tentang",
  wasIntroduce: "diperkenalkan kepada Anda melalui",
  veryLittle: "Sangat Sedikit",
  alot: "Banyak",
  phQuote: `"Orang Filipina tidak memiliki masalah anggaran, mereka memiliki masalah prioritas."`,
  quoteAuthor: `- Nama agen, kota`,
  helloIm: `Halo saya`,
  tryARole: `Coba bermain peran dengan saya!`,
  letTestYourSale: `Mari uji penjualan Anda dalam menjual `,
  setForLifeText: `Set for Life`,
  prepareReport: `Menyiapkan Laporan`,
  yourReport: `Laporan hasil Anda`,
  yourReports: `Laporan-laporan Anda`,
  yourScore: `Skor Anda`,
  explanation: `Penjelasan`,
  convoID: `ID Percakapan`,
  ssID: "Sesi",
  dateAndTime: "Tanggal dan Waktu",
  difficulty: "Tingkat Kesulitan",
  duration: "Durasi",
  beginner: "Pemula",
  expert: "Ahli",
  yourSkillSetDetail: "Detail Keterampilan Anda",
  tryANewSS: "Coba sesi baru",
  ohNo: "Oh tidak!",
  goToHome: "Ke beranda",
  goToMyReport: "Ke Laporan Saya",
  backToHome: "Kembali ke beranda",
  youHaveNotComplete: "Anda belum menyelesaikan sesi",
  congrats: "Selamat!",
  ssEnd: "Anda telah mencapai 60 hati! Sekarang sesi berakhir!",
  entryNew: "BARU",
  rolePlayTraningGuru: "Guru pelatihan bermain peran",
  testYourSalesSkills: "Uji keterampilan penjualan Anda",
  letsGo: "Ayo!",
  shapenSalesSkills: "Asah keterampilan penjualan dengan bermain peran AI",
  seeMoreDetails: "Lihat lebih detail",
  howYouCanDoBetter: "Lihat cara peningkatan",
  overallScore: "Nilai Anda",
  totalScore: "TOTAL SKOR",
  relationshipBuilding: "Membangun hubungan",
  customerDiscovery: "Penemuan pelanggan",
  productKnowledge: "Pengetahuan produk",
  objectionsAndClosing: "Penanganan Keberatan & Penutupan",
  speechAndAnalysis: "Pengucapan & kemampuan analisis",
  min: "mnt",
  sec: "dtk",
  thingsYouDidWell: "Hal yang Anda lakukan dengan baik",
  thingsYouDidnotDoWell: "Hal yang bisa ditingkatkan",
  improvements: "Area yang bisa ditingkatkan",
  trySayingThisNextTime: "Coba katakan ini lain kali",
  missComplete: "Waktu habis!",
  sessionComplete: "Sesi selesai",
  youCollected100:
    "Anda telah mengumpulkan cukup hati dan mencapai tujuan Anda",
  youCollected100QF:
    "Kerja Bagus,\n Anda telah meraih {{score}} hati. \n\n Coba mode bermain peran kami dan jual produk dalam 15 menit.",
  timeOut: "Waktu habis",
  tryAgain: "Coba lagi",
  viewReport: "Lihat hasil saya",
  letTryAgain: `Mari Coba Lagi!`,
  missFail: "Misi Berakhir",
  exitRole: "Keluar dari bermain peran",
  turnOnCamera: "Bisakah Anda menyalakan kamera?",
  toTalkWithRico:
    "Untuk berbicara dengan Rico, kami memerlukan izin Anda untuk menampilkan kamera",
  yesIAmOkayUsingCamera: "Ya, saya setuju menggunakan kamera",
  avatarNotReadyNotification:
    "Avatar belum siap, silakan kembali dan coba lagi nanti!",
  tryAgainToSeeMore: "Coba lagi untuk melihat umpan balik untuk kategori ini",
  setUpNextAppointment:
    "Atur janji temu berikutnya dengan pelanggan virtual Anda di bawah",
  pasSSPer: "Kinerja sesi masa lalu",
  viewAllSS: "Tampilkan semua sesi",
  yourPastSS: "Hasil laporan Anda",
  viewHistory: "Lihat riwayat",
  turnOn: "Nyalakan",
  goToSettings: "Ke pengaturan",
  microphonePermission: "Izinkan {{productName}} mengakses mikrofon Anda",
  turnOnMicrophone: "Bisakah Anda menyalakan mikrofon?",
  toTalkWithVirtualCustomer:
    "Untuk berbicara dengan pelanggan virtual Anda,\nsilakan izinkan akses ke mikrofon Anda",
  yesLetsProcess: "Ya, mari lanjutkan!",
  howUsefulDidYouFindTheReport: "Seberapa berguna laporan ini menurut Anda?",
  howWasYourExperience: "Bagaimana pengalaman Anda?",
  howWasTheSS: "Bagaimana sesinya?",
  yourReportIsReady: "Laporan Anda sudah siap!",
  viewMyReport: "Lihat laporan saya",
  yourMission: "Misi Anda",
  launchMission: "Luncurkan misi",
  thingsToImprove: "Poin pengembangan",
  thingsWeNailed: "Poin pencapaian",
  greatJob: "Kerja bagus, {{name}}!",
  myReport: "Laporan hasil Anda",
  seeFullTranscript: "Lihat transkrip lengkap",
  suggestedNextMove: "Saran untuk lain kali",

  "error.connection": "Maaf, ada yang salah, silakan coba lagi.",

  "feedback.overall.title": "Umpan Balik",

  "feedback.options.inaccurate": "Tidak akurat",
  "feedback.options.nothelpful": "Tidak membantu",
  "feedback.options.unhelpful": "Tidak berguna",
  "feedback.options.slow": "Lambat",
  "feedback.options.other": "Lainnya",

  "feedback.screen.ratingTitle": "Bagaimana sesinya?",
  "feedback.screen.commentTitle": "Ceritakan lebih lanjut",
  "feedback.screen.comment": "Komentar",
  "feedback.screen.submit": "Kirim",
  "feedback.screen.feedbackTitle1": "Apa yang tidak Anda sukai",
  "feedback.screen.feedbackTitle3": "Apa yang bisa kami perbaiki",

  "feedback.screen.thankyouTitle": "Terima kasih!",
  "feedback.screen.thankyouText":
    "Anda telah berhasil mengirimkan umpan balik Anda.",

  "feedback.form.title": "Mengapa Anda tidak menyukai jawabannya?",
  "feedback.form.submit": "Kirim",

  "modal.feedback.title": "Mengapa Anda tidak menyukai pesannya",
  "modal.feedback.comments": "Komentar",
  "modal.feedback.confirm": "Konfirmasi",

  trainingGuruIsUnderMaintenance: "Training Guru sedang dalam pemeliharaan",
  pleaseComeBackInAFewMinutes: "Silakan kembali dalam beberapa menit",

  youHaveEarnedOutOfHearts: "Anda telah meraih {{heart}} dari {{total}} hati!",
  youDidNotEarnAnyHearts:
    "Anda tidak meraih hati hari ini, tapi Anda selalu bisa mencoba lagi. Anda pasti bisa!",
  tryFullSessionExperience: "Coba pengalaman Sesi Lengkap",
  letsTryTheSalesRoleplay: "Coba Bermain Peran",
  letsTryAgain: "Coba lagi!",
  training: "Pelatih",
  guru: "Guru",
  quickfire: "Pengetahuan produk",
  ultimateRoleplay: "Bermain peran utama",
  ultimateRoleplayDesc:
    "Siap untuk tantangan? Simulasikan janji temu lengkap dan uji keterampilan Anda! jika Anda belum siap, mari kerjakan pembangun keterampilan di bawah!",
  objectionHandling: "Penganganan objeksi",
  objectionHandlingDesc: "Berlatih menghadapi keberatan nasabah dengan tepat",
  salesRolePlay: "Roleplay simulasi ",
  salesRolePlayDesc: "Analisa kebutuhan nasabah dan rekomendasikanlah produk BRI Life",
  exit: "Keluar",
  fullExp: "Pengalaman Lengkap",
  fullExpReport: "Laporan Pengalaman Lengkap",
  rolePlayModeReport: "Laporan Mode Bermain Peran",
  letSee: "Jawab sebanyak mungkin pertanyaan produk dalam ",
  enterASimulation: "Masuki simulasi dan coba jual produk dalam ",
  rpMin: "8 min",
  rpMinutes: "8 minutes",
  pkMin: "2 min",
  pkMinutes: "2 minutes",
  ohMin: "6 min",
  ohMinutes: "6 minutes",
  quickFireHiText: "Hai, {{name}}!",
  quickFireHiSubText: "Bersiap untuk menjual",
  watchVideo: "Tonton video contoh bermain peran",
  quickFireDesText:
    "Uji pengetahuan dengan menjawab pertanyaan sebanyak mungkin",
  leavingSoSoon: "Pergi begitu cepat?",

  isSadToSeeYouLeave: "{{avatarName}} sedih melihat Anda pergi :(",

  letsTryToAnswer: "Mari coba jawab",
  questionAboutTheProduct: "pertanyaan {{avatarName}} tentang produk!",

  hereYourGoal: "Ini tujuan Anda:",
  hereYourGoalQF: "2 mnt | Mode quickfire",
  hereYourGoalPK: "2 mnt | Pengetahuan produk",
  convinceAvatarToProduct:
    "Yakinkan {{avatarName}} untuk membeli {{productName}}",
  convinceAvatarToProductQF:
    "Jawab sebanyak mungkin pertanyaan untuk meraih poin!",
  rolePlayMode: "Mode bermain peran",
  productKnowledgeMode: "Mode pengetahuan produk",
  guideIntroText:
    "Anda adalah agen yang bertemu {{avatarName}}, prospek baru. {{agentPronounce}} seorang profesional muda yang dirujuk oleh teman. Tarik napas dalam-dalam dan bersiaplah!",
  ibBancaIntroText:
    "Anda adalah penasihat yang mendekati {{avatarName}}, prospek baru. Tujuan Anda memahami kebutuhan keuangannya dan secara efektif meyakinkannya untuk membeli {{productName}}. Tarik napas dalam-dalam dan bersiaplah!",
  startChat: "Mulai obrolan",
  startTheCall: "Mulai panggilan",
  smallTalkAndBuildRapport: "Obrolan ringan dan bangun hubungan",
  understandPersonalNeeds: "Pahami kebutuhan pribadi",
  presentProductOptions: "Presentasikan opsi produk",
  addressConcernsAndCloseTheDeal: "Tangani kekhawatiran dan tutup kesepakatan",
  appointmentSetting: "Pengaturan janji temu",
  faceToFaceMeeting: "Pertemuan tatap muka",
  appointmentSettingDesc:
    "Berlatih menelepon prospek untuk mengamankan janji temu pertemuan",
  productKnowledgeDesc:
    "Jawab sebanyak mungkin pertanyaan produk sebelum bertemu pelanggan",
  faceToFaceMeetingDesc:
    "Berlatih analisis kebutuhan dan rekomendasi produk di pertemuan penjualan",
  selectAIPersona: "Pilih persona AI untuk berlatih!",
  appointmentCallWith: "Mengatur panggilan janji temu dengan {{personaName}}",
  appointmentIntroduce: "Perkenalkan",
  appointmentBuildRapport: "Bangun hubungan dan nyatakan tujuan panggilan",
  appointmentInterest: "Bangkitkan minat",
  appointmentAskQuestion: "Ajukan pertanyaan untuk melibatkan mereka",
  appointmentSetTimeDate: "Atur waktu & tanggal",
  appointmentInsertATrial:
    "Masukkan penutupan percobaan di panggilan pertama Anda",
  appointmentConfirmMeetUp: "Konfirmasi pertemuan",
  appointmentGiveAssurance: "Berikan jaminan & ungkapkan rasa terima kasih",
  chatAgain: `Obrolan lagi`,
  exitAppointment: `Keluar`,
  timeUp: `Waktu habis!`,
  goalAchieve: `Tujuan tercapai!`,
  youHaveSecured: `Anda telah mengamankan janji temu dengan pelanggan`,
  yourInternetSpeed:
    "Kecepatan internet Anda terlalu lambat jadi kami mengubahnya menjadi panggilan audio",
  lowBandwidthTitle: "Koneksi internet lambat",
  lowBandwidthDescription:
    "Kami akan beralih ke mode audio saja untuk pengalaman yang lebih lancar.",
  lowBandwidthOk: "Mengerti!",
  noInternetTitle: "Ups! Tidak ada koneksi internet",
  noInternetDescription: "Silakan periksa jaringan Anda dan coba lagi",
  noInternetRetry: "Coba lagi",
  youHaveRanOutTime:
    "Anda kehabisan waktu. Periksa laporan Anda untuk melihat bagaimana Anda bisa berbuat lebih baik lain kali!",
  noHistoryYet: "Belum ada laporan. Mulai sesi untuk memulai!",
  howToWin: "Cara menang",
  objectionHandlingHeadline:
    'Dendy akan memulai percakapan dengan mengungkapkan keberatan dan kekhawatirannya. Dengarkan dengan seksama dan gunakan teori "DUIT" untuk menjawab.',
  tips: "Tips! ",
  rememberTheDUIT: "ingat kerangka DUIT",
  duitSteps: "D - Dengarkan   U - Ulangi   I - Isolasi   T - Tanggapi",
  startTheChat: "Mulai obrolan",
  thanksforPaticipating: "Terima kasih sudah berlatih!",
  selectObjectionScenario: "Pilih skenario objeksi",
  preparingYourReport: "Mempersiapkan laporan Anda...",
  videoCallLoading1: "Bentar ya, lagi siap-siap",
  videoCallLoading2: "Mic check 1, 2, 3",
  videoCallLoading3: "Cek suara dulu",
  videoCallLoading4: "Sabar, cari posisi biar pas",
  videoCallLoading5: "Sudah siap nih!",
  quitEarlyTitle: "Yakin mau keluar, nih?",
  earlyQuitDescription: "Dendy sedih melihat kamu pergi :(",
  earlyQuitContinue: "Tidak, saya ingin lanjut",
  continue: 'Lanjut',
  trainerGuru: "Trainer Guru",
  goodAfternoon: "Selamat siang!",
  letsPracticeWithAI: "Ayo berlatih dengan AI",
  sharpenSalesSkillsDescription: "Asah kemampuan sales Anda dan ajaklah nasabah AI untuk membeli produk BRI Life",
  tryWatchingRoleplayVideo: "Try watching the role-play video",
};
