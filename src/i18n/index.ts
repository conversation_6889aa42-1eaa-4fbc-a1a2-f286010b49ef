import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation files
import commonEn from './locales/en/common';
import ecoachEn from './locales/en/ecoach';
import commonB from './locales/bahasa/common';
import ecoachB from './locales/bahasa/ecoach';

// Define the resources
const resources = {
  en: {
    common: commonEn,
    ecoach: ecoachEn,
  },
    bahasa: {
    common: commonB,
    ecoach: ecoachB,
  },
};

// Initialize i18next
i18n
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources,
    lng: 'bahasa', // default language
    fallbackLng: 'en', // fallback language
    defaultNS: 'common', // default namespace
    keySeparator: false, // we do not use keys in form messages.welcome
    interpolation: {
      escapeValue: false, // react already does escaping
    },
    returnNull: false, // return key if translation is not found
    debug: process.env.NODE_ENV === 'development', // enable debug in development
  });

export default i18n;

// Export types for TypeScript
export type SupportedLanguages = keyof typeof resources;
export type Namespaces = keyof typeof resources.en;