import request from "./request";
import {
  ConversationData,
  ConversationFeedbackPayload,
  ConversationHistoryResponse,
  ConversationType,
  EcoachConfigItem,
  MaintenanceStatus,
} from "../types/ecoach";

export const VERIFY_CUBE_TOKEN_URL = "auth/verify_vn_cookie";

export const getVerifyCubeToken = () => {
  return request.get<{ jwt_token: string }>(`${VERIFY_CUBE_TOKEN_URL}`, {
    headers: { country: "bril" },
  });
};

export const getConfigurationData = () => {
  return request.get<EcoachConfigItem[]>("configuration/list", {
    headers: { country: "bril" },
  });
};

export const checkMaintenanceStatus = () => {
  return request.get<MaintenanceStatus>("maintenance/list", {
    headers: { country: "bril" },
  });
};

export const checkAvatarAvailability = () => {
  return request.get<{ voice_call_available: boolean }>(
    "conversation/available",
    {
      headers: { country: "bril" },
    }
  );
};

export type ReportHistoryInput = {
  conversationType?: ConversationType;
  limit?: number;
  pageParam?: string;
};

export const getConversationHistoryByPaging = ({
  body,
}: {
  body: ReportHistoryInput;
}) => {
  const {
    limit = 100,
    conversationType = ConversationType.FULL_EXPERIENCE,
    pageParam = "",
  } = body;
  return request.get<ConversationHistoryResponse>(
    `conversation/report_history_v2?limit=${limit}&conversation_type=${conversationType}${
      pageParam ? `&last_evaluated_key=${pageParam || ""}` : ""
    }`,
    {
      headers: { country: "bril" },
    }
  );
};

export const getConversationData = ({
  conversationId,
}: {
  conversationId: string;
}) => {
  return request.get<ConversationData>(
    `conversation/report/${conversationId}`,
    {
      headers: { country: "bril" },
    }
  );
};

export const submitConversationFeedback = (
  body: ConversationFeedbackPayload
) => {
  return request.post(
    "conversation/feedback",
    {
      ...body,
    },
    {
      headers: { country: "bril" },
    }
  );
};
