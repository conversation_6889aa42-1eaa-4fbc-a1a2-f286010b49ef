import React from "react";
import { HashRouter as Router, Routes, Route } from "react-router-dom";
import { Provider } from "react-redux";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "styled-components";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { store } from "@store/index";
import { theme } from "@styles/theme";
import { GlobalStyle } from "@styles/GlobalStyle";
import SessionHistory from "@screens/session-history/SessionHistory";
import TGHomePage from "@screens/home/<USER>";
import { VideoCallScreen } from "@screens/video-call";
import ReportScreen from "@screens/report";
import { ReportDetailsScreen } from "@screens/report-details";
import { ProductSelectionScreen } from "@screens/product-selection";
import { ProductLevelSelectionScreen } from "@screens/product-level-selection";
import {ObjectionHandlingSelectionScreen} from "@screens/objection-handling-selection";
import GuideLinesPage from "@screens/guidelines/GuideLinesPage.tsx";

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <GlobalStyle />
          <Router>
            <Routes>
              {/* Main ecoach routes */}
              <Route path="/" element={<TGHomePage />} />
              <Route path="/product-selection" element={<ProductSelectionScreen />} />
              <Route path="/product-level-selection" element={<ProductLevelSelectionScreen />} />
              <Route path="/objection-handling-selection" element={<ObjectionHandlingSelectionScreen />} />
              <Route path="/video-call" element={<VideoCallScreen />} />
              <Route path="/session-history" element={<SessionHistory />} />
              <Route path="/report" element={<ReportScreen />} />
              <Route path="/report/details" element={<ReportDetailsScreen />} />
              <Route path="/guidelines" element={<GuideLinesPage />} />

              {/* Redirect any unknown routes to home */}
              {/* <Route path="*" element={<Navigate to="/" replace />} /> */}
            </Routes>
          </Router>
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
          />
        </ThemeProvider>
      </Provider>
    </QueryClientProvider>
  );
}

export default App;
