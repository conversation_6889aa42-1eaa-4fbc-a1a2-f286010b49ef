import { createGlobalStyle } from "styled-components";

export const GlobalStyle = createGlobalStyle`
  html,
  body {
    font-family:
      "FWD",
      -apple-system,
      "sans-serif",
      "Segoe UI",
      "Roboto",
      "Oxygen",
      "Ubuntu",
      "Cantarell",
      "Fira Sans",
      "Droid Sans",
      "Helvetica Neue";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;

    width: 100%;
    padding: 0;
    margin: 0;

    color: ${({ theme }) => theme.color.text.body};
  }

  body {
    overflow: hidden !important;
  }

  *,
  :after,
  :before {
    box-sizing: border-box;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h7,
  .h8 {
    margin: 0;
    font-weight: 700;
    line-height: 125%;
  }

  h1 {
    font-size: 61px;
  }

  h2 {
    font-size: 49px;
  }

  h3 {
    font-size: 39px;
  }

  h4 {
    font-size: 31px;
  }

  h5 {
    font-size: 25px;
  }

  h6 {
    font-size: 20px;
  }

  .h7 {
    font-size: 16px;
  }

  .h8 {
    font-size: 14px;
  }

  p {
    margin: 0;
    font-size: 16px;
    line-height: 150%;
    color: ${({ theme }) => theme.color.text.body};
  }

  a {
    color: inherit;
    text-decoration: none;
  }

  * {
    box-sizing: border-box;
  }

  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  button {
    border: none;
    background: none;
    cursor: pointer;
    padding: 0;
    margin: 0;
  }

  input, textarea, select {
    font-family: inherit;
  }

  /* Mobile responsive breakpoints */
  @media (max-width: 768px) {
    h1 { font-size: 40px; }
    h2 { font-size: 32px; }
    h3 { font-size: 28px; }
    h4 { font-size: 24px; }
    h5 { font-size: 20px; }
    h6 { font-size: 18px; }
    .h7 { font-size: 14px; }
    .h8 { font-size: 12px; }
  }

  /* Utility classes */
  .text-center { text-align: center; }
  .text-left { text-align: left; }
  .text-right { text-align: right; }
  
  .d-flex { display: flex; }
  .d-block { display: block; }
  .d-none { display: none; }
  
  .justify-center { justify-content: center; }
  .justify-between { justify-content: space-between; }
  .align-center { align-items: center; }
  
  .w-100 { width: 100%; }
  .h-100 { height: 100%; }
  
  .color-primary { color: ${({ theme }) => theme.color.text.primary}; }
  .color-white { color: ${({ theme }) => theme.color.status.white}; }
`;
