import "styled-components";

declare module "styled-components" {
  export interface DefaultTheme {
    name: string;
    color: {
      text: {
        body: string;
        placeholder: string;
        primary: string;
        disabled: string;
      };
      background: {
        loadingBackground: string;
      };
      status: {
        primary: string;
        primary_5: string;
        primary_10: string;
        primary_15: string;
        primary_20: string;
        primary_30: string;
        primary_50: string;
        grey: string;
        grey_20: string;
        grey_50: string;
        grey_dark: string;
        grey_darker: string;
        grey_darkest: string;
        black: string;
        dark_green: string;
        dark_green_50: string;
        dark_green_20: string;
        dark_green_100: string;
        yellow: string;
        yellow_50: string;
        yellow_20: string;
        light_green: string;
        light_green_50: string;
        light_green_20: string;
        blue: string;
        blue_50: string;
        blue_20: string;
        red: string;
        red_light: string;
        green: string;
        green_20: string;
        green_light: string;
        white: string;
      };
    };
  }
}
