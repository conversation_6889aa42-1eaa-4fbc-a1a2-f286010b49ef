import styled from 'styled-components';
import { colors, sizes } from '@components/CubeBaseElement';

export const ButtonPrimary = styled.button`
  background-color: ${colors.brilBlue[50]};
  color: white;
  border: none;
  border-radius: ${sizes[2]}px;
  padding: ${sizes[3]}px ${sizes[4]}px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  
  // &:hover {
  //   background-color: ${colors.fwdOrange[100]};
  //   transform: translateY(-1px);
  // }
  
  // &:active {
  //   transform: translateY(0);
  // }
  
  &:disabled {
    background-color: ${colors.fwdGrey[50]};
    cursor: not-allowed;
    transform: none;
  }
`;

export const ButtonSecondary = styled.button`
  background-color: transparent;
  color: ${colors.fwdOrange[100]};
  border: 2px solid ${colors.fwdOrange[100]};
  border-radius: ${sizes[2]}px;
  padding: ${sizes[3]}px ${sizes[4]}px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  
  &:hover {
    background-color: ${colors.fwdOrange[100]};
    color: white;
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    border-color: ${colors.fwdGrey[50]};
    color: ${colors.fwdGrey[50]};
    cursor: not-allowed;
    transform: none;
  }
`;

export const ButtonText = styled.button`
  background-color: transparent;
  color: ${colors.fwdOrange[100]};
  border: none;
  padding: ${sizes[2]}px ${sizes[3]}px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: ${colors.fwdOrange[50]};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    color: ${colors.fwdGrey[50]};
    cursor: not-allowed;
    transform: none;
  }
`;

export const ButtonIcon = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  padding: ${sizes[2]}px;
  border-radius: ${sizes[1]}px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

export const BackBtn = styled.div`
  cursor: pointer;
  top: ${sizes[8]}px;
  left: ${sizes[4]}px;
  & svg {
    transform: rotate(180deg);
  }
  position: absolute;
  color: ${colors.white};
`;
