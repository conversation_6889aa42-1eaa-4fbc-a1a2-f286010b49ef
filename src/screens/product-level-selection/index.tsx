import ModalFullPage from "@components/modal-full-page";
import React, { useCallback, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import useWindowResize from "@hooks/useWindowResize";
import {
  <PERSON>tonContainer,
  H6,
  PageContainer,
  ProductCardContainer,
  ProductContainer,
  SmallLabel,
  Title,
  TitleContainer,
} from "./styled";
import { ButtonPrimary } from "@styles/buttons";
import { useDispatch } from "react-redux";
import { useAppSelector } from "@store/hooks";
import LoadingSection from "@components/loading";
import { DifficultType } from "../../@custom-types/ecoach";

const ProductLevelSelectionScreen = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [params] = useSearchParams();
  const productFlowType = params.get("productFlowType") || "";
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const { width: windowWidth } = useWindowResize();

  const getEcoachConfigurationDataLoading = useAppSelector(state => state.app.loading);
  const productConfig = useAppSelector(state => state.ecoach.productConfig);
  const quickfireProductConfig = useAppSelector(state => state.ecoach.quickfireProductConfig);

  const handleNext = useCallback(() => {
    if (!selectedType) {
      return;
    }

    navigate(`/video-call?productFlowType=${params.toString()}&difficultType=${selectedType}`);
  }, [selectedType, productFlowType]);

  return (
    <ModalFullPage show={true} title="Chọn Cấp Độ Sản Phẩm" onClose={() => navigate(-1)}>
      <PageContainer>
        <Title>Difficulty</Title>

        {Boolean(getEcoachConfigurationDataLoading) ? (
          <div className="mt-16">
            <LoadingSection loading={true} />
          </div>
        ) : (
          <>
            <ProductContainer columns="repeat(auto-fit, minmax(290px, 400px))" gap={16} mbColumns={1}>
              <ProductCard
                key={"Beginner"}
                title={"Beginner"}
                description={"Are you new to sales or need to review basic knowledge? Start here!"}
                isSelected={selectedType === DifficultType.Beginner}
                onSelect={setSelectedType}
              />
              <ProductCard
                key={"Expert"}
                title={"Expert"}
                description={"Do you have experience? Try this level to improve your skills!"}
                isSelected={selectedType === DifficultType.Expert}
                onSelect={setSelectedType}
              />
            </ProductContainer>

            <ButtonContainer>
              <ButtonPrimary
                style={{
                  maxWidth: "15rem",
                  width: "100%",
                }}
                disabled={!selectedType}
                onClick={handleNext}
              >
                Tiếp tục
              </ButtonPrimary>
            </ButtonContainer>
          </>
        )}
      </PageContainer>
    </ModalFullPage>
  );
};

interface ProductCardProps {
  title: string;
  description: string;
  isSelected: boolean;
  onSelect: (productCode: string) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ title, description, isSelected, onSelect }) => {
  return (
    <ProductCardContainer isSelected={isSelected} onClick={() => onSelect?.(title)}>
      <TitleContainer>
        <H6 isSelected={isSelected}>{title}</H6>
        <SmallLabel>{description}</SmallLabel>
      </TitleContainer>
    </ProductCardContainer>
  );
};

export { ProductLevelSelectionScreen };
