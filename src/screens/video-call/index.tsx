import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

import Loading from "@components/loading";
import CircularProgressTimer, {
  DEFAULT_CALL_TIME,
  SIX_MIN_CALL_TIME,
  TWO_MIN_CALL_TIME,
} from "../../components/ProgressTimer";
import { CircleEllipsis } from "../../components/icons/CircleEllipsis";
import { MicOff } from "../../components/icons/MicOff";
import { MicOn } from "../../components/icons/MicOn";
import { XIcon } from "../../components/icons/Close";
import { EllipsisAnimated } from "../../components/EllipsisAnimated";
import {
  ActionButtonContainer,
  AvatarName,
  AvatarThinkingContainer,
  CloseButtonWrapper,
  ContentContainer,
  HeaderView,
  HeartImage,
  LoadingContainer,
  Mi<PERSON><PERSON><PERSON>onWrapper,
  StatusBar<PERSON>mage,
  VideoCallPageContainer,
} from "./styled";
import { checkAvatarAvailability } from "../../api/ecoach";
import useActionApi from "@hooks/use-action-api";
import WebRTC from "../../components/webrtc";
import { customAlphabet } from "nanoid/non-secure";
import { ProductFlowType } from "../../@custom-types/ecoach";
import { useAppSelector } from "@store/hooks";
import VideoCallLoadingModal from "../../components/modal/VideoCallLoadingModal";
import AvatarNotReady from "../../components/AvatarNotReady";
import {
  emptyStatus,
  heart,
  heartAni,
  heartBroken,
  stageEightStatus,
  stageFiveStatus,
  stageFourStatus,
  stageOneStatus,
  stageSevenStatus,
  stageSixStatus,
  stageTwoStatus,
} from "../../assets";
import Lottie from "lottie-react";
import { useExitModal } from "@/components/CallExitModal";
import styled from "styled-components";
import { VideoStreamingV2 } from "@/components/video-streaming/video-streaming-v2";

const RootContainer = styled.div`
  height: 100dvh;
  width: 100dvw;
  overflow: hidden !important;
`;

const LottieContainer = styled.div`
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  pointer-events: none;
`;

const VideoCallScreen: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const quickfireCompletedHearts = useAppSelector(
    (state) => state.ecoach.quickfireCompletedHearts
  );

  const productFlowType = (searchParams.get("productFlowType") ||
    "") as ProductFlowType;
  const productSelectionCode = searchParams.get("productSelectionCode") || "";
  const difficultType = searchParams.get("difficultType") || "";

  const [muted, setMuted] = useState(false);
  const [thinking, setThinkingState] = useState(false);
  const [isAvailable, setIsAvailable] = useState(true);
  const [avatarIsReady, setAvatarIsReady] = useState(false);
  const [avatarUuid, setAvatarUuid] = useState("");
  const [heartScore, setHeartScore] = useState(0);
  const [previousHeartScore, setPreviousHeartScore] = useState(0);
  const [websocket, setWebSocket] = useState<WebSocket | null>(null);
  const [avatarName, setAvatarName] = useState(
    searchParams.get("avatar") || "Vinh"
  );
  const [endCallFlag, setEndCallFlag] = useState(false);
  const [leavingModalVisible, setLeavingModalVisible] = useState(false);
  const [showTimeOutModal, setShowTimeOutModal] = useState(false);
  const [showCountDownCircle, setShowCountDownCircle] = useState(true);
  const [conversationId, setConversationId] = useState<string>("");
  const { openModal, getModalData, modalContent } = useExitModal();
  const websocketRef = useRef<{
    close: (done?: () => void) => void;
    retake: (conversationId: string) => void;
  }>(null);

  const [showHeartBroken, setShowHeartBroken] = useState(false);
  const [showHeart, setShowHeart] = useState(false);
  const actionCheckAvatarAvailability = useActionApi(checkAvatarAvailability);

  const [reTryTime, setReTryTime] = useState(0);
  let callTime = DEFAULT_CALL_TIME;
  if (productFlowType === ProductFlowType.QUICKFIRE) {
    callTime = TWO_MIN_CALL_TIME;
  } else if (productFlowType === ProductFlowType.OBJECTION_HANDLING) {
    callTime = SIX_MIN_CALL_TIME;
  }
  const [totalTime, setTotalTime] = useState(callTime);

  const isQuickfireProduct = useMemo(
    () => productFlowType === ProductFlowType.QUICKFIRE,
    [productFlowType]
  );
  console.log(conversationId, "conversationId");
  console.log(isQuickfireProduct, "isQuickfireProduct");
  console.log(websocket, "websocket");
  const isObjectionHandling = useMemo(
    () => productFlowType === ProductFlowType.OBJECTION_HANDLING,
    [productFlowType]
  );
  const isLowScore = useMemo(() => {
    return heartScore < quickfireCompletedHearts;
  }, [heartScore, quickfireCompletedHearts]);

  useEffect(() => {
    const conversationId = customAlphabet(
      "abcdefghijklmnopqrstuvwxyz0123456789",
      21
    )();
    setConversationId(conversationId);
  }, []);

  useEffect(() => {
    actionCheckAvatarAvailability
      .execute({
        loading: {
          type: "local",
          name: "checkAvatarAvailabilityLoading",
        },
      })
      .then((response: any) => {
        setIsAvailable(true);
        if (response?.data?.voice_call_available) {
          setIsAvailable(true);
        } else {
          setIsAvailable(false);
          setTimeout(() => {
            navigate("/");
          }, 3000);
        }
      })
      .catch(() => {
        navigate("/");
      });
  }, []);

  const toggleMute = useCallback(() => {
    setMuted((prev) => !prev);
  }, []);

  // Effect for keypress event
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent): void => {
      if (event.code === "Space") {
        event.preventDefault();
        toggleMute();
      }
    };

    // Add event listener
    window.addEventListener("keydown", handleKeyPress);

    // Cleanup
    return () => {
      window.removeEventListener("keydown", handleKeyPress);
    };
  }, [muted]); // Depend on muted state

  const resetTotalTime = () => {
    setTotalTime(
      productFlowType === ProductFlowType.QUICKFIRE
        ? TWO_MIN_CALL_TIME
        : DEFAULT_CALL_TIME
    );
  };

  const handleTimeout = () => {
    setShowTimeOutModal(true);
    if (isQuickfireProduct || isObjectionHandling) {
      if (isLowScore) {
        openModal(
          getModalData("task-failed", {
            primaryAction: endCallAndGoReport,
            secondaryAction: reTake,
          })
        );
      } else {
        openModal(
          getModalData("completed", {
            primaryAction: endCallAndGoReport,
            secondaryAction: reTake,
          })
        );
      }
    } else {
      // full experience
      openModal(
        getModalData("completed", {
          primaryAction: endCallAndGoReport,
          secondaryAction: reTake,
        })
      );
    }
  };

  const handleExitCall = () => {
    openModal(
      getModalData("early-quit", {
        secondaryAction: endCallAndGoReport,
      })
    );
  };

  useEffect(() => {
    if (endCallFlag) {
      handleTimeout();
    }
  }, [endCallFlag]);

  const cleanUp = () => {
    setConversationId("");
    setEndCallFlag(false);
    // setShowFailModal(false);
    // setShowTimeOutModal(false);
    setAvatarIsReady(false);
    setHeartScore(0);
    setWebSocket(null);
    resetTotalTime();
  };

  const reTake = () => {
    cleanUp();
    setShowCountDownCircle(true);
    setReTryTime(reTryTime + 1);

    const newConversationId = customAlphabet(
      "abcdefghijklmnopqrstuvwxyz0123456789",
      21
    )();
    setConversationId(newConversationId);

    websocketRef.current?.retake(newConversationId);
  };

  const endCallAndGoReport = () => {
    cleanUp();
    navigate(
      `/report?${searchParams.toString()}&conversationId=${conversationId}`
    );
    websocketRef.current?.close();
  };

  const getStatusBarImage = () => {
    if (heartScore >= 80) {
      return stageEightStatus;
    } else if (heartScore >= 70) {
      return stageSevenStatus;
    } else if (heartScore >= 60) {
      return stageSixStatus;
    } else if (heartScore >= 50) {
      return stageFiveStatus;
    } else if (heartScore >= 40) {
      return stageFourStatus;
    } else if (heartScore >= 30) {
      return stageTwoStatus;
    } else if (heartScore >= 20) {
      return stageOneStatus;
    } else {
      return emptyStatus;
    }
  };

  useEffect(() => {
    if (!conversationId) return;
    if (heartScore - previousHeartScore >= 5) {
      setShowHeart(true);
      setTimeout(() => {
        setShowHeart(false);
      }, 2500);
    }
    if (previousHeartScore - heartScore >= 5) {
      setShowHeartBroken(true);
      setTimeout(() => {
        setShowHeartBroken(false);
      }, 1500);
    }
    setPreviousHeartScore(heartScore);

  }, [conversationId, heartScore, previousHeartScore]);

  if (!isAvailable) {
    return <AvatarNotReady />;
  }

  return (
    <RootContainer>
      {modalContent}
      {websocket && conversationId ? (
        <VideoCallPageContainer>
          {isQuickfireProduct && (
            <LottieContainer>
              {showHeartBroken && (
                <Lottie animationData={heartBroken} autoPlay={false} loop={false}/>
              )}
              {showHeart && (
                <Lottie animationData={heartAni} autoPlay={true} loop={false}/>
              )}
            </LottieContainer>
          )}
          {avatarUuid && (
            <VideoStreamingV2
              isFocused
              conversationId={conversationId}
              isTimeOut={showTimeOutModal}
              avatarId={avatarUuid || ""}
              onVideoReady={() => setAvatarIsReady(true)}
            />
          )}

          {isQuickfireProduct ? (
            <HeaderView>
              <StatusBarImage src={getStatusBarImage()} alt={""} />
              <HeartImage src={heart} alt={""} />
            </HeaderView>
          ) : null}

          <ContentContainer>
            {thinking ? (
              <AvatarThinkingContainer>
                <EllipsisAnimated>
                  <CircleEllipsis stroke="white" />
                </EllipsisAnimated>
                <div>{avatarName} is thinking</div>
              </AvatarThinkingContainer>
            ) : (
              <></>
            )}

            <AvatarName>{avatarName}</AvatarName>

            <ActionButtonContainer>
              <CircularProgressTimer
                setLeavingModalVisible={setLeavingModalVisible}
                setTimeOutStatus={handleTimeout}
                timeRemainingNotification={isQuickfireProduct ? 30 : 60}
                totalTime={totalTime}
                reTryTime={reTryTime}
              />
              <MicButtonWrapper onClick={toggleMute}>
                {muted ? (
                  <MicOff stroke={"none"} fill={"black"} />
                ) : (
                  <MicOn stroke={"none"} fill={"black"} />
                )}
              </MicButtonWrapper>
              <CloseButtonWrapper onClick={handleExitCall}>
                <XIcon stroke={"white"} fill={"none"} />
              </CloseButtonWrapper>
            </ActionButtonContainer>
          </ContentContainer>
        </VideoCallPageContainer>
      ) : (
        <LoadingContainer>
          <Loading loading={true} />
        </LoadingContainer>
      )}

      <>
        {showCountDownCircle && (
          <VideoCallLoadingModal
            visible={showCountDownCircle}
            avatarIsReady={avatarIsReady}
            onComplete={() => setShowCountDownCircle(false)}
          />
        )}
      </>

      {isAvailable && conversationId ? (
        <WebRTC
          ref={websocketRef}
          conversationId={conversationId || ""}
          muted={muted}
          productFlowType={productFlowType}
          productSelectionCode={productSelectionCode}
          isTimeOut={showTimeOutModal}
          onSocketConnected={() => null}
          setAvatarIsReady={setAvatarIsReady}
          setAvatarUuid={setAvatarUuid}
          setThinkingState={setThinkingState}
          setWebSocket={setWebSocket}
          setHeartScore={setHeartScore}
          setAvatarName={setAvatarName}
          setEndCallFlag={setEndCallFlag}
        />
      ) : (
        <div></div>
      )}
    </RootContainer>
  );
};

export { VideoCallScreen };
