import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import {H6, H7, H8, colors, sizes, Spacer, View} from '../../components/CubeBaseElement';
import { SkillDetail } from '../../@custom-types/ecoach';
import {
  arrowLeft,
  doubleQuoteMark,
  thingsDidnotDoWellMark,
  thingsDidWellMark,
} from '../../assets';

// Simple Pagination Component
const PaginationContainer = styled.div(() => ({
  alignSelf: 'center',
  marginTop: sizes[3],
  flexDirection: 'row',
  gap: sizes[1],
}));

const PaginationDot = styled.div<{ $active: boolean }>(({ $active }) => ({
  width: 8,
  height: 8,
  borderRadius: 4,
  backgroundColor: $active ? colors.fwdOrange[100] : colors.fwdGrey[100],
  cursor: 'pointer',
  transition: 'background-color 0.2s ease',
}));

interface PaginationProps {
  count: number;
  activeIndex: number;
  onPressDot: (index: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({ count, activeIndex, onPressDot }) => (
  <PaginationContainer>
    {Array.from({ length: count }, (_, index) => (
      <PaginationDot
        key={index}
        $active={index === activeIndex}
        onClick={() => onPressDot(index)}
      />
    ))}
  </PaginationContainer>
);

enum ReportStatus {
  NA = 'N/A',
  YES = 'YES',
  NO = 'NO',
}

const CARD_WIDTH_WITH_MARGIN = 350; // Fixed width for consistency

const PageContainer = styled.div(() => ({
  flex: 1,
  marginTop: sizes[6],
  padding: sizes[4],
  minHeight: '100vh',
  backgroundColor: colors.white,
}));

const Content = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  gap: sizes[12],
  padding: sizes[6],
}));

const ArrowLeftImage = styled.img(() => ({
  width: sizes[6],
  height: sizes[6],
  cursor: 'pointer',
}));

const SkillNameTitle = styled(H6)(() => ({
  color: colors.fwdDarkGreen[100],
}));

const HeaderText = styled(H7)(() => ({
  color: colors.fwdDarkGreen[100],
  marginTop: sizes[1],
  marginBottom: sizes[5],
}));

const HeaderTryNextTimeText = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
  marginBottom: sizes[2],
}));

const ListItemView = styled.div(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  marginVertical: sizes[1],
}));

const MarkImage = styled.img(() => ({
  width: sizes[6],
  height: sizes[6],
  marginRight: sizes[2],
}));

const DoubleQuoteMarkContainer = styled.div(() => ({
  flexDirection: 'row',
  alignSelf: 'flex-start',
}));

const DoubleQuoteMarkImage = styled.img(() => ({
  width: 36,
  height: 28,
  marginTop: sizes[4],
  marginRight: sizes[4],
}));

const FlatListContainer = styled.div(() => ({
  marginBottom: sizes[4],
  width: '100%',
}));

type Improvement = {
  improvement: string;
  try_next_time: string;
};

const ImprovementText = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
  fontStyle: 'normal',
  lineHeight: sizes[5],
  paddingTop: sizes[2],
  paddingBottom: sizes[2],
}));

const ImprovementItemContainer = styled.div(() => ({
  marginBottom: 16,
  width: CARD_WIDTH_WITH_MARGIN,
}));

const TryNextTimeContainer = styled.div(() => ({
  borderRadius: sizes[2],
  backgroundColor: colors.fwdGrey[20],
  padding: sizes[4],
  marginTop: sizes[4],
}));

const TryNextTimeText = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
}));

const ListText = styled(H8)(() => ({
  flexWrap: 'wrap',
  flex: 1,
  color: colors.fwdDarkGreen[100],
}));

const ScrollContainer = styled.div(() => ({
  flexGrow: 1,
  overflowY: 'auto',
}));

const HorizontalScrollContainer = styled.div(() => ({
  width: CARD_WIDTH_WITH_MARGIN,
  overflowX: 'hidden',
  position: 'relative',
}));

const ImprovementCarousel = styled.div<{ $translateX: number }>(({ $translateX }) => ({
  display: 'flex',
  flexDirection: 'row',
  transform: `translateX(${$translateX}px)`,
  transition: 'transform 0.3s ease',
}));

const DetailSummaryTabletPage = () => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const skillName = searchParams.get('skill') || '';
  const skillSetParam = searchParams.get('skillSet') || '[]';
  
  let skillSet: { [key: string]: SkillDetail }[] = [];
  try {
    skillSet = JSON.parse(decodeURIComponent(skillSetParam));
  } catch (error) {
    console.error('Error parsing skillSet:', error);
  }

  const [curPage, setCurPage] = useState(0);

  const goBack = () => {
    navigate(-1);
  };

  const processData = (skillSet: { [key: string]: SkillDetail }[]) => {
    const thingsDidWell: string[] = [];
    const thingsDidNotDoWell: string[] = [];
    const improvements: Improvement[] = [];

    skillSet.forEach(item => {
      const key = Object.keys(item)[0];
      const entry: SkillDetail = item[key];
      if (
        entry.assessment !== ReportStatus.NA &&
        entry.atom_summary !== ReportStatus.NA
      ) {
        if (entry.assessment == ReportStatus.YES) {
          thingsDidWell.push(entry.atom_summary);
        } else if (entry.assessment == ReportStatus.NO) {
          thingsDidNotDoWell.push(entry.atom_summary);
          if (
            entry.improvement !== ReportStatus.NA &&
            entry.try_next_time !== ReportStatus.NA
          ) {
            improvements.push({
              improvement: entry.improvement,
              try_next_time: entry.try_next_time,
            });
          }
        }
      }
    });

    return { thingsDidWell, thingsDidNotDoWell, improvements };
  };

  const { thingsDidWell, thingsDidNotDoWell, improvements } = processData(skillSet);

  const onPressDot = (index: number) => {
    setCurPage(index);
  };

  return (
    <PageContainer>
      <ArrowLeftImage src={arrowLeft} alt="Go back" onClick={goBack} />
      <Spacer height={sizes[8]} />
      <ScrollContainer>
        <SkillNameTitle fontWeight={'bold'}>{skillName}:</SkillNameTitle>
        <Spacer height={sizes[4]} />

        <Content>
          <View style={{ minWidth: 300 }}>
            {thingsDidWell && thingsDidWell.length > 0 && (
              <>
                <HeaderText fontWeight={'bold'}>
                  {t('thingsYouDidWell')}:
                </HeaderText>
                <FlatListContainer>
                  {thingsDidWell.map((item, index) => (
                    <ListItemView key={index}>
                      <MarkImage src={thingsDidWellMark} alt="Things did well" />
                      <ListText>{item}</ListText>
                    </ListItemView>
                  ))}
                </FlatListContainer>
              </>
            )}

            {thingsDidNotDoWell && thingsDidNotDoWell.length > 0 && (
              <>
                <HeaderText fontWeight={'bold'}>
                  {t('thingsYouDidnotDoWell')}:
                </HeaderText>
                <FlatListContainer>
                  {thingsDidNotDoWell.map((item, index) => (
                    <ListItemView key={index}>
                      <MarkImage src={thingsDidnotDoWellMark} alt="Things did not do well" />
                      <ListText>{item}</ListText>
                    </ListItemView>
                  ))}
                </FlatListContainer>
              </>
            )}
          </View>

          <View style={{ width: CARD_WIDTH_WITH_MARGIN }}>
            {improvements && improvements.length > 0 && (
              <>
                <HeaderText fontWeight={'bold'}>
                  {t('improvements')}:
                </HeaderText>
                <FlatListContainer>
                  <HorizontalScrollContainer>
                    <ImprovementCarousel 
                      $translateX={-curPage * CARD_WIDTH_WITH_MARGIN}
                    >
                      {improvements.map((item, index) => (
                        <ImprovementItemContainer key={index}>
                          <ImprovementText>{item.improvement}</ImprovementText>
                          <TryNextTimeContainer>
                            <HeaderTryNextTimeText fontWeight={'bold'}>
                              {t('trySayingThisNextTime')}:
                            </HeaderTryNextTimeText>
                            <TryNextTimeText>
                              {item.try_next_time}
                            </TryNextTimeText>
                            <DoubleQuoteMarkContainer>
                              <DoubleQuoteMarkImage src={doubleQuoteMark} alt="Quote mark" />
                            </DoubleQuoteMarkContainer>
                          </TryNextTimeContainer>
                        </ImprovementItemContainer>
                      ))}
                    </ImprovementCarousel>
                  </HorizontalScrollContainer>
                  <Pagination
                    count={improvements?.length ?? 0}
                    activeIndex={curPage}
                    onPressDot={onPressDot}
                  />
                </FlatListContainer>
              </>
            )}
          </View>
        </Content>

        {thingsDidWell?.length == 0 &&
          thingsDidNotDoWell?.length == 0 &&
          improvements?.length == 0 && (
            <ListText>{t('tryAgainToSeeMore')}</ListText>
          )}
      </ScrollContainer>
    </PageContainer>
  );
};

export default DetailSummaryTabletPage; 