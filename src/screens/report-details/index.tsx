import React from 'react';
import DetailSummaryPage from './DetailSummary';
import DetailSummaryTabletPage from './DetailSummary.tablet';
import useLayoutAdoptionCheck from "@hooks/useLayoutAdoptionCheck";

export const ReportDetailsScreen: React.FC = () => {
  const { isTabletMode } = useLayoutAdoptionCheck();

  if (isTabletMode) {
    return <DetailSummaryTabletPage />;
  }

  return <DetailSummaryPage />;
}; 