import React from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { H7, H8, colors, sizes, Spacer, H5} from '../../components/CubeBaseElement';
import { SkillDetail } from '../../@custom-types/ecoach';
import {
  doubleQuoteMark,
  thingsDidnotDoWellMark,
  thingsDidWellMark,
} from '../../assets';
import ArrowLeftIcon from "@components/icons/ArrowLeftIcon";

enum ReportStatus {
  NA = 'N/A',
  YES = 'YES',
  NO = 'NO',
}

const PageContainer = styled.div(() => ({
  flex: 1,
  paddingTop: sizes[10],
  paddingBottom: sizes[4],
  minHeight: '100vh',
  backgroundColor: colors.white,
}));

const BackBtn = styled.button(() => ({
  background: 'transparent',
  border: 'none',
  cursor: 'pointer',
  paddingLeft: sizes[4],
  paddingRight: sizes[4],
}));

const SkillNameTitle = styled(H5)(() => ({
  color: colors.fwdDarkGreen[100],
  paddingLeft: sizes[4],
  paddingRight: sizes[4],
}));

const HeaderView = styled.div(() => ({
  marginTop: sizes[2],
  marginBottom: sizes[2],
}));

const HeaderText = styled(H7)(() => ({
  color: colors.fwdDarkGreen[100],
  paddingLeft: sizes[4],
  paddingRight: sizes[4],
}));

const HeaderTryNextTimeText = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
}));

const ListItemView = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  marginTop: sizes[4],
  marginBottom: sizes[4],
  paddingLeft: sizes[4],
  paddingRight: sizes[4],
}));

const MarkImage = styled.img(() => ({
  width: sizes[6],
  height: sizes[6],
  marginRight: sizes[2],
}));

const DoubleQuoteMarkContainer = styled.div(() => ({
  flexDirection: 'row',
  alignSelf: 'flex-end',
}));

const DoubleQuoteMarkImage = styled.img(() => ({
  width: 36,
  height: 28,
}));

const FlatListContainer = styled.div(() => ({
  marginBottom: sizes[4],
  width: '100%',

}));

type Improvement = {
  improvement: string;
  try_next_time: string;
};

const ImprovementText = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
  paddingTop: sizes[2],
  paddingBottom: sizes[2],
}));

const ImprovementItemContainer = styled.div(() => ({
  paddingTop: sizes[4],
  paddingLeft: sizes[4],
  paddingRight: sizes[4],
}));

const TryNextTimeContainer = styled.div(() => ({
  borderRadius: sizes[2],
  backgroundColor: colors.fwdGrey[20],
  marginTop: sizes[4],
  marginBottom: sizes[4],
  padding: sizes[4],
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
}));

const TryNextTimeText = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
}));

const ListText = styled(H8)(() => ({
  flexWrap: 'wrap',
  flex: 1,
  color: colors.fwdDarkGreen[100],
}));

const ScrollContainer = styled.div(() => ({
  flexGrow: 1,
  paddingLeft: sizes[2],
  paddingRight: sizes[2],
  maxHeight: 'calc(100vh - 100px)',
  overflowY: 'auto',
  paddingBottom: '50px',
  scrollbarWidth: 'none',
}));

const DetailSummaryPage = () => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  
  const skillName = searchParams.get('skill') || '';
  const skillSet = location.state?.skillSet || [];
  const processData = (skillSet: { [key: string]: SkillDetail }[]) => {
    const thingsDidWell: string[] = [];
    const thingsDidNotDoWell: string[] = [];
    const improvements: Improvement[] = [];

    skillSet.forEach(item => {
      const key = Object.keys(item)[0];
      const entry: SkillDetail = item[key];
      if (
        entry.assessment !== ReportStatus.NA &&
        entry.atom_summary !== ReportStatus.NA
      ) {
        if (entry.assessment == ReportStatus.YES) {
          thingsDidWell.push(entry.atom_summary);
        } else if (entry.assessment == ReportStatus.NO) {
          thingsDidNotDoWell.push(entry.atom_summary);
          if (
            entry.improvement !== ReportStatus.NA &&
            entry.try_next_time !== ReportStatus.NA
          ) {
            improvements.push({
              improvement: entry.improvement,
              try_next_time: entry.try_next_time,
            });
          }
        }
      }
    });

    return { thingsDidWell, thingsDidNotDoWell, improvements };
  };
  
  const { thingsDidWell, thingsDidNotDoWell, improvements } = processData(skillSet);

  return (
    <PageContainer>
      <BackBtn onClick={() => navigate(-1)}>
        <ArrowLeftIcon fill={colors.fwdDarkGreen[100]} size={24} />
      </BackBtn>
      <Spacer height={sizes[2]} />
      <ScrollContainer>
        <SkillNameTitle fontWeight={'bold'}>{skillName}:</SkillNameTitle>
        <Spacer height={sizes[6]} />
        {thingsDidWell && thingsDidWell.length > 0 && (
          <>
            <HeaderView>
              <HeaderText fontWeight={'bold'}>
                {t('thingsYouDidWell')}:
              </HeaderText>
            </HeaderView>

            <FlatListContainer>
              {thingsDidWell.map((item, index) => (
                <ListItemView key={index}>
                  <MarkImage src={thingsDidWellMark} alt="Things did well" />
                  <ListText>{item}</ListText>
                </ListItemView>
              ))}
            </FlatListContainer>
          </>
        )}

        {thingsDidNotDoWell && thingsDidNotDoWell.length > 0 && (
          <>
            <HeaderView>
              <HeaderText fontWeight={'bold'}>
                {t('thingsYouDidnotDoWell')}:
              </HeaderText>
            </HeaderView>

            <FlatListContainer>
              {thingsDidNotDoWell.map((item, index) => (
                <ListItemView key={index}>
                  <MarkImage src={thingsDidnotDoWellMark} alt="Things did not do well" />
                  <ListText>{item}</ListText>
                </ListItemView>
              ))}
            </FlatListContainer>
          </>
        )}

        {improvements && improvements.length > 0 && (
          <>
            <HeaderText fontWeight={'bold'}>{t('improvements')}:</HeaderText>
            <FlatListContainer>
              {improvements.map((item, index) => (
                <ImprovementItemContainer key={index}>
                  <ImprovementText>{item.improvement}</ImprovementText>
                  <TryNextTimeContainer>
                    <HeaderTryNextTimeText fontWeight={'bold'}>
                      {t('trySayingThisNextTime')}:
                    </HeaderTryNextTimeText>
                    <TryNextTimeText>{item.try_next_time}</TryNextTimeText>
                    <DoubleQuoteMarkContainer>
                      <DoubleQuoteMarkImage src={doubleQuoteMark} alt="Quote mark" />
                    </DoubleQuoteMarkContainer>
                  </TryNextTimeContainer>
                </ImprovementItemContainer>
              ))}
            </FlatListContainer>
          </>
        )}
        
        {thingsDidWell?.length == 0 &&
          thingsDidNotDoWell?.length == 0 &&
          improvements?.length == 0 && (
            <ListText>{t('tryAgainToSeeMore')}</ListText>
          )}
      </ScrollContainer>
    </PageContainer>
  );
};

export default DetailSummaryPage; 