import React, { useMemo } from "react";
import {
  colors,
  H3,
  H4,
  H7,
  <PERSON>Label,
  SmallBody,
  Spacer,
} from "@components/CubeBaseElement";
import { lounge<PERSON>ri, cafeBGMobile, cafeBGTablet } from "../../assets";
import useWindowResize from "@hooks/useWindowResize";
import { useAppSelector } from "@store/hooks";
import { useNavigate, useSearchParams } from "react-router-dom";
import FullScreenPage from "@components/FullScreenPage";
import { ECOACH_COUNTRY, ProductFlowType } from "../../@custom-types/ecoach";
import OperatingHoursIcon from "@components/icons/OperatingHoursIcon";
import { ButtonPrimary, BackBtn } from "@styles/buttons";
import { GuideLinesStyle } from "./styled";
import { ChevronRight } from "@/components/icons";
import {
  BackButton,
  BackgroundImageOverlay,
  DarkBackgroundOverlay,
  ScreenContainer,
  ScreenContent,
  ScreenFooter,
  ScreenHeader,
} from "@/components/ScreenContainer";

const DividerHorizontal = () => (
  <GuideLinesStyle.HorizontalDividerView>
    <GuideLinesStyle.HorizontalDivider />
  </GuideLinesStyle.HorizontalDividerView>
);

const GuideLinesPage = () => {
  const { width } = useWindowResize();
  const noMobile = width > 768;
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const productFlowType = searchParams.get("productFlowType") || "";
  const productSelectionCode = searchParams.get("productSelectionCode") || "";
  const productConfig = useAppSelector((state) => state.ecoach.productConfig);
  const quickfireProductConfig = useAppSelector(
    (state) => state.ecoach.quickfireProductConfig
  );
  const objectionHandlingProductConfig = useAppSelector(
    (state) => state.ecoach.objectionHandlingProductConfig
  );
  const homepageBackground = useAppSelector(
    (state) => state.ecoach.homepageBackground
  );

  const productName = useMemo(() => {
    let productForCurrentCountry;
    if (productFlowType === ProductFlowType.QUICKFIRE) {
      productForCurrentCountry = quickfireProductConfig?.[ECOACH_COUNTRY];
    } else if (productFlowType === ProductFlowType.OBJECTION_HANDLING) {
      productForCurrentCountry =
        objectionHandlingProductConfig?.[ECOACH_COUNTRY];
    } else {
      productForCurrentCountry = productConfig?.[ECOACH_COUNTRY]; // fallback to FULL_EXPERIENCE
    }

    return (
      productForCurrentCountry?.find(
        (product) => product.product_code === productSelectionCode
      )?.product_name || "Aurora Plus"
    );
  }, [productConfig, quickfireProductConfig, productFlowType]);

  const goVideoPage = () => navigate(`/video-call?${searchParams}`);
  let productTypeText = "";
  let titleText = "";
  let descriptionText = "";
  let oneToFourTextList: string[] = [];

  if (productFlowType === ProductFlowType.FULL_EXPERIENCE) {
    productTypeText = "8 min | Roleplay simulasi";
    titleText = `Mengajak Dendy untuk membeli ${productName}`;
    descriptionText =
      "Anda berperan sebagai agent yang akan bertemu Dendy, seorang prospek baru. Beliau adalah profesional muda yang dikenalkan oleh temanmu. Tarik napas dan bersiaplah!";
    oneToFourTextList = [
      "Obrolan ringan dan bangun hubungan",
      "Memahami kebutuhan pribadi prospek",
      "Menyarankan dan menjelaskan produk",
      "Mengangani keberatan dan closing penjualan",
    ];
  } else if (productFlowType === ProductFlowType.QUICKFIRE) {
    productTypeText = "2 min | Pengetahuan produk";
    titleText = `Jawablah pertanyaan sebanyak banyaknya untuk dapatkan poin!`;
    descriptionText = "";
  } else if (productFlowType === ProductFlowType.OBJECTION_HANDLING) {
    productTypeText = "6 min | Pengetahuan produk";
    titleText = "Tanggapilah objeksi dari Dendy dengan “DUIT”";
    descriptionText =
      "Dendy akan memulai percakapan dengan menyampaikan objeksi dan kekhawatirannya. Dengarkanlah dengan baik dan gunakanlah teori “DUIT” untuk menjawab.";
  }

  return (
    <>
      <DarkBackgroundOverlay />
      <BackgroundImageOverlay />
      <ScreenContainer>
        <ScreenHeader>
          <BackButton onClick={() => navigate(-1)} />
        </ScreenHeader>
        <ScreenContent>
          <GuideLinesStyle.Container>
            <GuideLinesStyle.ConvinceAvatarView>
              {!noMobile && (
                <GuideLinesStyle.YourGoal>
                  <SmallBody fontWeight="bold" color={"#0179CE"}>
                    {productTypeText}
                  </SmallBody>
                </GuideLinesStyle.YourGoal>
              )}
              <GuideLinesStyle.ConvinceText>
                {noMobile ? (
                  <H3 fontWeight="bold" color={colors.white}>
                    {titleText}
                  </H3>
                ) : (
                  <H4 fontWeight="bold" color={colors.white}>
                    {titleText}
                  </H4>
                )}
                {noMobile && (
                  <GuideLinesStyle.ProductSelectView>
                    <LargeLabel fontWeight="500" color={colors.fwdOrange[100]}>
                      {productFlowType === ProductFlowType.QUICKFIRE
                        ? "Quickfire Mode"
                        : "Role-play Mode"}
                    </LargeLabel>
                    <GuideLinesStyle.TimeView>
                      <LargeLabel fontWeight="normal" color={colors.white}>
                        |
                      </LargeLabel>
                      <OperatingHoursIcon size={16} fill={colors.white} />
                      <LargeLabel fontWeight="normal" color={colors.white}>
                        {productFlowType === ProductFlowType.QUICKFIRE
                          ? "2 minutes"
                          : "8 minutes"}
                      </LargeLabel>
                    </GuideLinesStyle.TimeView>
                  </GuideLinesStyle.ProductSelectView>
                )}
              </GuideLinesStyle.ConvinceText>
              <GuideLinesStyle.IntroText>
                {descriptionText}
              </GuideLinesStyle.IntroText>
              {productFlowType === ProductFlowType.OBJECTION_HANDLING && (
                <GuideLinesStyle.IntroTextBold>
                  D - Dengarkan U - Ulangi I - Isolasi T - Tanggapi
                </GuideLinesStyle.IntroTextBold>
              )}

              {noMobile && (
                <ButtonPrimary
                  style={{ maxWidth: 180, marginTop: 32 }}
                  onClick={goVideoPage}
                >
                  Ayo mulai!
                </ButtonPrimary>
              )}
            </GuideLinesStyle.ConvinceAvatarView>
            <GuideLinesStyle.YourGoalViewTablet>
              {noMobile && (
                <GuideLinesStyle.YourGoal>
                  <SmallBody fontWeight="bold" color={"#0179CE"}>
                    Your goal:
                  </SmallBody>
                </GuideLinesStyle.YourGoal>
              )}
              <GuideLinesStyle.OneToFour>
                {oneToFourTextList.map((text, index) => (
                  <React.Fragment key={index}>
                    <GuideLinesStyle.NumberRow>
                      <GuideLinesStyle.Circle>
                        <H7 fontWeight="bold" color={colors.white}>
                          {index + 1}
                        </H7>
                      </GuideLinesStyle.Circle>
                      <H7 fontWeight="bold" color={colors.white}>
                        {text}
                      </H7>
                    </GuideLinesStyle.NumberRow>
                    {index < 3 && <DividerHorizontal />}
                  </React.Fragment>
                ))}

                {/* {!noMobile && (
                  <>
                    <Spacer height={48} />
                    <ButtonPrimary onClick={goVideoPage}>
                      Ayo mulai!
                    </ButtonPrimary>
                    <Spacer height={48} />
                  </>
                )} */}
              </GuideLinesStyle.OneToFour>
            </GuideLinesStyle.YourGoalViewTablet>
          </GuideLinesStyle.Container>
        </ScreenContent>
        <ScreenFooter>
          <ButtonPrimary style={{ width: "100%" }} onClick={goVideoPage}>
            Ayo mulai!
          </ButtonPrimary>
        </ScreenFooter>
      </ScreenContainer>
    </>
  );

  return (
    <FullScreenPage
      showHeader={false}
      backgroundImage={noMobile ? loungeBri : loungeBri}
    >
      <GuideLinesStyle.PageContainer>
        <BackBtn onClick={() => navigate(-1)}>
          <ChevronRight />
        </BackBtn>
        <GuideLinesStyle.Container>
          <GuideLinesStyle.ConvinceAvatarView>
            {!noMobile && (
              <GuideLinesStyle.YourGoal>
                <SmallBody fontWeight="bold" color={"#0179CE"}>
                  {productTypeText}
                </SmallBody>
              </GuideLinesStyle.YourGoal>
            )}
            <GuideLinesStyle.ConvinceText>
              {noMobile ? (
                <H3 fontWeight="bold" color={colors.white}>
                  {titleText}
                </H3>
              ) : (
                <H4 fontWeight="bold" color={colors.white}>
                  {titleText}
                </H4>
              )}
              {noMobile && (
                <GuideLinesStyle.ProductSelectView>
                  <LargeLabel fontWeight="500" color={colors.fwdOrange[100]}>
                    {productFlowType === ProductFlowType.QUICKFIRE
                      ? "Quickfire Mode"
                      : "Role-play Mode"}
                  </LargeLabel>
                  <GuideLinesStyle.TimeView>
                    <LargeLabel fontWeight="normal" color={colors.white}>
                      |
                    </LargeLabel>
                    <OperatingHoursIcon size={16} fill={colors.white} />
                    <LargeLabel fontWeight="normal" color={colors.white}>
                      {productFlowType === ProductFlowType.QUICKFIRE
                        ? "2 minutes"
                        : "8 minutes"}
                    </LargeLabel>
                  </GuideLinesStyle.TimeView>
                </GuideLinesStyle.ProductSelectView>
              )}
            </GuideLinesStyle.ConvinceText>
            <GuideLinesStyle.IntroText>
              {descriptionText}
            </GuideLinesStyle.IntroText>
            {productFlowType === ProductFlowType.OBJECTION_HANDLING && (
              <GuideLinesStyle.IntroTextBold>
                D - Dengarkan U - Ulangi I - Isolasi T - Tanggapi
              </GuideLinesStyle.IntroTextBold>
            )}

            {noMobile && (
              <ButtonPrimary
                style={{ maxWidth: 180, marginTop: 32 }}
                onClick={goVideoPage}
              >
                Ayo mulai!
              </ButtonPrimary>
            )}
          </GuideLinesStyle.ConvinceAvatarView>
          <GuideLinesStyle.YourGoalViewTablet>
            {noMobile && (
              <GuideLinesStyle.YourGoal>
                <SmallBody fontWeight="bold" color={"#0179CE"}>
                  Your goal:
                </SmallBody>
              </GuideLinesStyle.YourGoal>
            )}
            <GuideLinesStyle.OneToFour>
              {oneToFourTextList.map((text, index) => (
                <React.Fragment key={index}>
                  <GuideLinesStyle.NumberRow>
                    <GuideLinesStyle.Circle>
                      <H7 fontWeight="bold" color={colors.white}>
                        {index + 1}
                      </H7>
                    </GuideLinesStyle.Circle>
                    <H7 fontWeight="bold" color={colors.white}>
                      {text}
                    </H7>
                  </GuideLinesStyle.NumberRow>
                  {index < 3 && <DividerHorizontal />}
                </React.Fragment>
              ))}

              {!noMobile && (
                <>
                  <Spacer height={48} />
                  <ButtonPrimary onClick={goVideoPage}>
                    Ayo mulai!
                  </ButtonPrimary>
                  <Spacer height={48} />
                </>
              )}
            </GuideLinesStyle.OneToFour>
          </GuideLinesStyle.YourGoalViewTablet>
        </GuideLinesStyle.Container>
      </GuideLinesStyle.PageContainer>
    </FullScreenPage>
  );
};

export default GuideLinesPage;
