import React, { FC, useMemo, useRef, useState } from "react";
import styled from "styled-components";
import {
  colors,
  H4,
  H6,
  H7,
  H8,
  sizes,
  Spacer,
} from "../../components/CubeBaseElement";
import {
  dendy as avatarMobile,
  dendy as avatarTablet,
  lounge<PERSON><PERSON> as cafeBGMobile,
  lounge<PERSON><PERSON> as cafeBGTablet,
} from "../../assets";
import NavigationCard, {
  NavigationCardType,
} from "../../components/cards/NavigationCard";
import SessionQuickFireCard from "../../components/cards/SessionQuickFireCard";
import {
  ConversationData,
  ProductFlowType,
  ConversationType,
  ECOACH_COUNTRY,
} from "../../@custom-types/ecoach";
import useWindowResize from "@hooks/use-window-resize";
import { useReportHistoryLatest } from "../../hooks/useReportHistoryLatest";
import { useAppSelector } from "@store/hooks";
import { breakpoints, device } from "@styles/media";
import CloseIcon from "../../assets/icons/CloseIcon";
import LoadingSection from "@components/loading";
import { useNavigate } from "react-router-dom";
import FullScreenPage from "../../components/FullScreenPage";
import useMaintenance from "../../hooks/useMaintenance";
import MaintenancePage from "../../components/Maintenance";
import FeedbackModal from "../../components/modal/FeedbackModal";
import { useGetEcoachConfigurationData } from "@hooks/useGetEcoachConfigurationData.ts";
import { TabItem, Tabs } from "@/components/tabs";
import { useTranslation } from "react-i18next";
import { ModuleAvailability } from "@/types/ecoach";
import { orderBy, uniqBy } from "lodash";
import { INSETS } from "@/utils/insets";
import useMyActivity from "@/hooks/use-my-activity";
import { ScreenContainer, ScreenContent } from "@/components/ScreenContainer";

const Container = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  background: linear-gradient(
    180deg,
    rgba(1, 1, 1, 0.5) 5.61%,
    rgba(1, 1, 1, 0) 16.81%,
    rgba(1, 1, 1, 0) 27.69%,
    #010101 52.64%
  );

  @media ${device.noMobile} {
    background: linear-gradient(
      0deg,
      #000 47%,
      rgba(0, 0, 0, 0) 65.55%,
      rgba(0, 0, 0, 0) 88.41%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
`;

const AvatarImg = styled.img`
  position: absolute;
  top: 50px;
  width: 100%;
  max-width: 400px;
  z-index: -1;
  left: 50%;
  transform: translateX(-50%);
  @media ${device.noMobile} {
    object-fit: cover;
    height: 550px;
    right: 25%;
    width: 50%;
  }
`;

const HorizontalView = styled.div`
  display: flex;
  width: 100%;
  max-width: ${breakpoints.sm}px;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-left: ${sizes[4]}px;
  padding-right: ${sizes[4]}px;
  gap: ${sizes[8]}px;
`;

const Header = styled.div`
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  margin-top: ${INSETS.top}px;
  padding-left: ${sizes[4]}px;
  padding-right: ${sizes[4]}px;
`;

const HeaderRightView = styled.div`
  display: flex;
  gap: ${sizes[4]}px;
  flex-direction: row;
  justify-content: space-between;
`;

const HiView = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: 202px;
  margin-bottom: 32px;
  @media ${device.noMobile} {
    margin-top: 285px;
    margin-bottom: 52px;
  }
`;

const HiText = styled(H4)`
  text-align: center;
  margin-left: ${sizes[4]}px;
  margin-right: ${sizes[4]}px;
`;

const DesText = styled(H7)`
  text-align: center;
  align-self: center;
  padding-top: ${sizes[2]}px;
  margin: 0 ${sizes[4]}px;
`;

const WatchVideo = styled.div`
  display: flex;
  margin-top: ${sizes[4]}px;
  width: 100%;
  align-items: center;
  justify-content: center;
  background-color: transparent;
`;

const DividerView = styled.div`
  margin-top: ${sizes[10]}px;
  margin-bottom: ${sizes[2]}px;
  width: calc(100% - ${sizes[4]}px);
  height: 1px;
  background: #8b9793;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
`;

const SessionView = styled.div`
  padding-left: ${sizes[4]}px;
  padding-right: ${sizes[4]}px;
  gap: ${sizes[4]}px;
  padding-bottom: ${sizes[4]}px;
  margin-bottom: ${sizes[10]}px;
  width: 100%;
  max-width: ${breakpoints.sm}px;
`;
const Content = styled.div`
  @media ${device.noMobile} {
    display: flex;
    gap: ${sizes[4]}px;
    justify-content: space-between;
    margin-bottom: ${sizes[4]}px;
  }
`;

const Btn = styled.div`
  cursor: pointer;
  display: flex;
  justify-content: center;
  height: fit-content;
  width: fit-content;
  margin: 0 auto;
  padding: ${sizes[2]}px;
`;

const TGHomePage = () => {
  const { t } = useTranslation("ecoach");
  const { width } = useWindowResize();
  const noMobile = width > 768;
  const navigate = useNavigate();
  const bottomRef = useRef<HTMLDivElement>(null);
  const { ecoachConfig } = useGetEcoachConfigurationData(true);
  const homepageBackground = useAppSelector(
    (state) => state.ecoach.homepageBackground
  );
  const quickfireVideoUrl = useAppSelector(
    (state) => state.ecoach.quickfireVideoUrl
  );
  const { quitWebView, accessToken } = useMyActivity();
  // console.log('TGHomePage homepageBackground', homepageBackground)
  const validUrl =
    quickfireVideoUrl &&
    quickfireVideoUrl.length > 0 &&
    quickfireVideoUrl.includes("https://");

  const { isMaintenanceInProgress } = useMaintenance();
  const [feedbackVisible, setFeedbackVisible] = useState(false);

  const goToSelectPolicy = () => {
    const productConfig = ecoachConfig?.productConfig?.[ECOACH_COUNTRY];
    if (!productConfig) return;
    if (productConfig && productConfig?.length == 1) {
      const productSelectionCode = productConfig[0]?.product_code;
      navigate(
        `/guidelines?productFlowType=${ProductFlowType.FULL_EXPERIENCE}&productSelectionCode=${productSelectionCode}`
      );
    } else {
      navigate(
        `/product-selection?productFlowType=${ProductFlowType.FULL_EXPERIENCE}`
      );
    }
  };

  const goToQuickFireCall = () => {
    const quickfireProductConfig =
      ecoachConfig?.quickfireProductConfig?.[ECOACH_COUNTRY];
    if (!quickfireProductConfig) return;
    if (quickfireProductConfig && quickfireProductConfig?.length == 1) {
      const productSelectionCode = quickfireProductConfig[0]?.product_code;
      navigate(
        `/guidelines?productFlowType=${ProductFlowType.QUICKFIRE}&productSelectionCode=${productSelectionCode}`
      );
    } else {
      navigate(
        `/product-selection?productFlowType=${ProductFlowType.QUICKFIRE}`
      );
    }
  };

  const goToObjectionHandling = () => {
    const objectionHandlingProductConfig =
      ecoachConfig?.objectionHandlingProductConfig?.[ECOACH_COUNTRY];
    if (!objectionHandlingProductConfig) return;
    if (
      objectionHandlingProductConfig &&
      objectionHandlingProductConfig?.length == 1
    ) {
      const productSelectionCode =
        objectionHandlingProductConfig[0]?.product_code;
      navigate(
        `/guidelines?productFlowType=${ProductFlowType.FULL_EXPERIENCE}&productSelectionCode=${productSelectionCode}`
      );
    } else {
      navigate(
        `/objection-handling-selection?productFlowType=${ProductFlowType.OBJECTION_HANDLING}`
      );
    }
  };

  const goToWatchVideoPage = () => {
    if (validUrl) {
      window.open(quickfireVideoUrl, "_blank");
    }
  };

  const closeBtn = () => {
    // navigate("/", { replace: true });
    quitWebView();
  };

  const getBGImage = () => {
    if (noMobile) {
      return homepageBackground?.background_image_tablet || cafeBGTablet;
    } else {
      return homepageBackground?.background_image_mobile || cafeBGMobile;
    }
  };
  const getAvatarImage = () => {
    if (noMobile) {
      return homepageBackground?.avatar_image_tablet || avatarTablet;
    } else {
      return homepageBackground?.avatar_image_tablet || avatarMobile;
    }
  };

  if (isMaintenanceInProgress) {
    return <MaintenancePage />;
  }

  if (!(ecoachConfig && accessToken)) {
    return (
      <ScreenContainer
        style={{ backgroundImage: "none", backgroundColor: "#FFFBF6" }}
      >
        <ScreenContent style={{ display: "grid", placeItems: "center" }}>
          <LoadingSection loading />
        </ScreenContent>
      </ScreenContainer>
    );
  }

  return (
    <FullScreenPage backgroundImage={getBGImage()}>
      <AvatarImg src={getAvatarImage()} alt="doc-img" />
      <>
        {feedbackVisible && (
          <FeedbackModal
            visible={feedbackVisible}
            setVisible={setFeedbackVisible}
            title="Overall Feedback"
            feedbackType="OVERALL"
          />
        )}
      </>
      <Container>
        <Header>
          <div>
            <img src="img/bri-logo.svg" alt="bri-logo" />
            <H8 fontWeight="bold" color={"#FEF9F4"}>
              {t("trainerGuru")}
            </H8>
          </div>
          <HeaderRightView>
            <Btn onClick={closeBtn}>
              <CloseIcon fill={colors.white} size={30} />
            </Btn>
          </HeaderRightView>
        </Header>
        <HiView>
          <HiText fontWeight={"bold"} color={"white"}>
            {t("goodAfternoon")}
          </HiText>
          <HiText fontWeight={"bold"} color={"white"}>
            {t("letsPracticeWithAI")}
          </HiText>
          <DesText fontWeight={"normal"} color={"white"}>
            {t("sharpenSalesSkillsDescription")}
          </DesText>
        </HiView>
        <HorizontalView>
          {ecoachConfig &&
            ecoachConfig.moduleAvailability?.face_to_face_meeting && (
              <NavigationCard
                navigationCardType={NavigationCardType.SalesRolePlay}
                onPress={goToSelectPolicy}
              />
            )}
          {ecoachConfig &&
            ecoachConfig.moduleAvailability?.product_knowledge && (
              <NavigationCard
                navigationCardType={NavigationCardType.Quickfire}
                onPress={goToQuickFireCall}
              />
            )}
          {ecoachConfig &&
            ecoachConfig.moduleAvailability?.objection_handling && (
              <NavigationCard
                navigationCardType={NavigationCardType.ObjectionHandling}
                onPress={goToObjectionHandling}
              />
            )}
        </HorizontalView>

        {validUrl && (
          <WatchVideo onClick={goToWatchVideoPage}>
            <H8 fontWeight="bold" color={colors.fwdOrange[100]}>
              {t("tryWatchingRoleplayVideo")}
            </H8>
          </WatchVideo>
        )}

        <DividerView></DividerView>

        <SessionView>
          <Spacer height={sizes[4]} />
          <H6 fontWeight="bold" color={colors.white}>
            {t("yourPastSS")}
          </H6>
          <Spacer height={sizes[4]} />
          <RecentSessions
            moduleAvailability={ecoachConfig?.moduleAvailability}
          />
        </SessionView>
        <div ref={bottomRef} style={{ height: "1px" }} />
      </Container>
    </FullScreenPage>
  );
};

export default TGHomePage;

export type RecentSessionsProps = {
  moduleAvailability?: ModuleAvailability;
};

export const RecentSessions: FC<RecentSessionsProps> = ({
  moduleAvailability,
}) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);

  let TABS = [];

  if (moduleAvailability?.face_to_face_meeting) {
    TABS.push({
      label: "Roleplay simulasi",
      conversationType: ConversationType.FULL_EXPERIENCE,
    });
  }

  if (moduleAvailability?.product_knowledge) {
    TABS.push({
      label: "Pengetahuan produk",
      conversationType: ConversationType.QUICKFIRE,
    });
  }

  if (moduleAvailability?.objection_handling) {
    TABS.push({
      label: "Penanganan objeksi",
      conversationType: ConversationType.OBJECTION_HANDLING,
    });
  }

  return (
    <>
      <Tabs activeIndex={activeTabIndex} onChange={setActiveTabIndex}>
        {TABS.map((tab, index) => (
          <TabItem key={index}>{tab.label}</TabItem>
        ))}
      </Tabs>
      <Spacer height={sizes[4]} />
      {TABS.map((tab, index) => (
        <SessionList
          key={index}
          isActive={activeTabIndex === index}
          conversationType={tab.conversationType}
        />
      ))}
    </>
  );
};

export type SessionListProps = {
  conversationType: ConversationType;
  isActive?: boolean;
};

export const SessionList: FC<SessionListProps> = ({
  conversationType,
  isActive,
}) => {
  const { t } = useTranslation("ecoach");
  const navigate = useNavigate();
  const { data, loading } = useReportHistoryLatest(5, conversationType);
  const sessionList = useMemo(() => {
    if (!data) {
      return [];
    }
    return data.slice(0, 3);
  }, [data]);
  const hasMoreItems = useMemo(() => {
    if (!data) {
      return false;
    }
    return data.length > 3;
  }, [data]);
  const rows: ConversationData[] = orderBy(
    uniqBy(sessionList, "conversation_id"),
    "datetime",
    "desc"
  );

  const goToSSHistory = () => {
    navigate(`/session-history?conversationType=${conversationType}`);
  };

  if (!isActive) {
    return null;
  }

  if (loading && rows.length === 0) {
    return <LoadingSection loading={true} />;
  }

  return (
    <>
      {rows.map((item) => (
        <Content key={`row-${item.conversation_id}`}>
          <SessionQuickFireCard
            session={item}
            sessionNumber={item.session_number || ""}
            key={item.conversation_id}
          />
        </Content>
      ))}
      {hasMoreItems && (
        <>
          <Spacer height={sizes[4]} />
          <Btn onClick={goToSSHistory}>
            <H8 fontWeight="bold" color={colors.brilBlue[100]}>
              {t("viewAllSS")}
            </H8>
          </Btn>
        </>
      )}
    </>
  );
};
