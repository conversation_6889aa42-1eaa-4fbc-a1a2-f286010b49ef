import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  TitleDiv,
  ProductCardContainer,
  ProductContainer,
  ProductImage,
  ProductImageContainer,
  SmallLabel,
  TitleContainer,
  TitleText,
  ButtonText,
} from "./styled";
import { ButtonPrimary } from "@styles/buttons";
import { useAppSelector } from "@store/hooks";
import { useSearchParams } from "react-router-dom";
import {
  ECOACH_COUNTRY,
  EcoachProductConfig,
  ProductFlowType,
} from "../../@custom-types/ecoach";
import { useTranslation } from "react-i18next";
import {
  BackButton,
  BackgroundImageOverlay,
  DarkBackgroundOverlay,
  ScreenContainer,
  ScreenContent,
  ScreenFooter,
  ScreenHeader,
} from "@/components/ScreenContainer";
import { H5 } from "@/components/CubeBaseElement";
import styled from "styled-components";

const SubmitButton = styled(ButtonPrimary)`
  width: 100%;
`;

const ObjectionHandlingSelectionScreen = () => {
  const { t } = useTranslation("ecoach");
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const productFlowType = searchParams.get("productFlowType") || "";
  const [selectedProductCode, setSelectedProductCode] = useState<string | null>(
    null
  );

  const getEcoachConfigurationDataLoading = useAppSelector(
    (state) => state.app.loading
  );
  const userChannel = useAppSelector((state) => state.app.user.channel);
  const productConfig = useAppSelector((state) => state.ecoach.productConfig);
  const quickfireProductConfig = useAppSelector(
    (state) => state.ecoach.quickfireProductConfig
  );
  const objectionHandlingProductConfig = useAppSelector(
    (state) => state.ecoach.objectionHandlingProductConfig
  );

  const availableProducts = useMemo(() => {
    let productForCurrentCountry;
    if (productFlowType === ProductFlowType.FULL_EXPERIENCE) {
      productForCurrentCountry = productConfig?.[ECOACH_COUNTRY];
    } else if (productFlowType === ProductFlowType.QUICKFIRE) {
      productForCurrentCountry = quickfireProductConfig?.[ECOACH_COUNTRY];
    } else if (productFlowType === ProductFlowType.OBJECTION_HANDLING) {
      productForCurrentCountry =
        objectionHandlingProductConfig?.[ECOACH_COUNTRY];
    }
    if (productForCurrentCountry) {
      return [...productForCurrentCountry].sort((a, b) => a.order - b.order);
    }

    return [];
  }, [
    productConfig,
    quickfireProductConfig,
    objectionHandlingProductConfig,
    productFlowType,
    userChannel,
  ]);

  const handleNext = useCallback(() => {
    if (!selectedProductCode) {
      return;
    }
    navigate(
      `/guidelines?productFlowType=${productFlowType}&productSelectionCode=${selectedProductCode}&difficultType=Beginner`
    );
  }, [selectedProductCode, productFlowType]);

  useEffect(() => {
    const isSingleProduct = availableProducts.filter((e) => e.clickable);
    if (isSingleProduct.length === 1) {
      setSelectedProductCode(isSingleProduct[0].product_code);
    }
  }, [availableProducts]);

  return (
    <>
      <DarkBackgroundOverlay />
      <BackgroundImageOverlay />
      <ScreenContainer>
        <ScreenHeader>
          <BackButton onClick={() => navigate(-1)} />
          <H5 fontWeight="bold">{t("selectObjectionScenario")}</H5>
        </ScreenHeader>
        <ScreenContent>
          <ProductContainer
            $columns="repeat(auto-fit, minmax(290px, 400px))"
            $gap={16}
            $mbColumns={2}
          >
            {availableProducts.map((product) => (
              <ProductCard
                key={product.product_code}
                product={product}
                $isSelected={selectedProductCode === product.product_code}
                onSelect={setSelectedProductCode}
              />
            ))}
          </ProductContainer>
        </ScreenContent>
        <ScreenFooter>
          <SubmitButton disabled={!selectedProductCode} onClick={handleNext}>
            <ButtonText $isSelected={!selectedProductCode}>
              {t("continue")}
            </ButtonText>
          </SubmitButton>
        </ScreenFooter>
      </ScreenContainer>
    </>
  );
};

interface ProductCardProps {
  product: EcoachProductConfig;
  onSelect: (productCode: string) => void;
}

const ProductCard: React.FC<ProductCardProps & { $isSelected: boolean }> = ({
  product,
  $isSelected,
  onSelect,
}) => {
  const {
    product_name,
    product_code,
    product_description,
    product_image,
    product_image_base64,
    clickable,
  } = product;

  return (
    <ProductCardContainer
      $isSelected={$isSelected}
      disabled={!clickable}
      onClick={() => clickable && onSelect?.(product_code)}
    >
      <ProductImageContainer>
        <ProductImage src={product_image || product_image_base64 || ""} />
      </ProductImageContainer>
      <TitleContainer>
        <TitleDiv>
          <TitleText>{product_name}</TitleText>
        </TitleDiv>
        <SmallLabel>{product_description}</SmallLabel>
      </TitleContainer>
    </ProductCardContainer>
  );
};

export { ObjectionHandlingSelectionScreen };
