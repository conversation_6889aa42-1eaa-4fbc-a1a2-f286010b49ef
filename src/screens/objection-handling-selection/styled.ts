import styled from "styled-components";
import { device } from "@styles/media";
import { colors, sizes } from "../../components/CubeBaseElement";

export const TOTAL_ITEMS_PER_ROW = 3;
export const cardContainerPadding = 16;
export const cardPadding = 16;

interface GridProps {
  className?: string;
  $columns?: string | number;
  $gap?: number;
  $columnGap?: number;
  $rowGap?: number;
  height?: string;
  minRowHeight?: string;
  flow?: string;
  $rows?: string | number;
  justifyContent?: string;
  alignContent?: string;
  maxWidthChildren?: boolean;
  $mbColumns?: number;
}

const layoutGenerate = (value: any) => (typeof value === "number" ? `repeat(${value}, 1fr)` : value);

export const PageContainer = styled.div<{ paddingBottom?: number }>`
  flex: 1;
  height: 100vh;
  padding: ${cardContainerPadding}px;
  background: linear-gradient(180deg, rgba(1, 1, 1, 0.92) 5.61%, rgba(1, 1, 1, 0.82) 18.79%, rgba(1, 1, 1, 0.92) 51.74%);
  @media ${device.noMobile} {
    flex-direction: row;
    background: linear-gradient(90deg, rgba(1, 1, 1, 0.92) 33.19%, rgba(1, 1, 1, 0.92) 93.58%);
    gap: ${sizes[8]}px;
  }
`;

export const ProductContainer = styled.div<GridProps>`
  display: grid;
  height: ${({ height = "auto" }) => height};

  grid-auto-flow: ${({ flow = "row" }) => flow};
  grid-auto-rows: ${({ minRowHeight }) => minRowHeight && `minmax(${minRowHeight}, auto)`};

  grid-template-columns: ${({ $columns = 12 }) => layoutGenerate($columns)};
  grid-template-rows: ${({ $rows }) => layoutGenerate($rows)};

  grid-gap: ${({ $gap }) => $gap}px;
  column-gap: ${({ $columnGap }) => $columnGap}px;
  row-gap: ${({ $rowGap }) => $rowGap}px;

  ${({ justifyContent }) => justifyContent && `justify-content: ${justifyContent}`};
  ${({ alignContent }) => alignContent && `align-content: ${alignContent}`};

  ${({ maxWidthChildren }) =>
    maxWidthChildren &&
    `
  div{
    max-width:100%;
  }
  `}

  @media ${device.mobile} {
    grid-template-columns: ${({ $mbColumns }) => layoutGenerate($mbColumns)};
  }
`;

export const TitleContainer = styled.div`
  width: 100%;
  padding: 1rem;
  text-align: left;

  @media ${device.tablet} {
    width: 50%;
  }
`;

export const Title = styled.h4`
  color: white;
  margin-top: 4rem;
  margin-bottom: 2rem;
  font-weight: bold;
`;

export const ProductImageContainer = styled.div`
  width: 100%;
  height: 100px;
  overflow: hidden;
  margin-top: 1rem;
  margin-left: 1rem;
  margin-right: 1rem;
  display: flex;
  justify-content: center;
`;

export const ProductImage = styled.div<{ src: string }>`
  width: 90%;
  min-height: 100px;
  border-radius: 8px;

  background: ${({ src }) => `url(${src})`};
  background-position: center center;
  background-size: cover;

  @media ${device.tablet} {
    margin-left: 8px;
  }
`;

export const ProductCardContainer = styled.div<{
  $isSelected: boolean;
  disabled?: boolean;
}>`
  width: 100%;
  min-height: 190px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px;
  border: 1px solid ${({ $isSelected }) => ($isSelected ? colors.brilBlue[100] : "white")};
  padding: 0;
  cursor: pointer;
`;

export const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  padding: 24px 0;
  margin-bottom: 50px;
  margin-top: 50px;
  @media ${device.mobile} {
    & > button {
      width: 100% !important;
      max-width: 100% !important;
    }
  }
`;

export const ButtonText = styled.h6<{
  $isSelected: boolean;
}>`
  color: ${({ $isSelected }) => ($isSelected ? "rgba(0, 0, 0, 0.38)" : "white")};
`;

export const TitleDiv = styled.div`
  margin-bottom: 4px;
`;

export const TitleText = styled.h5`
  font-weight: bold;
  text-align: left;
  color: white;
`;

export const SmallLabel = styled.div`
  font-weight: normal;
  color: white;
  text-align: left;
  font-size: 0.8rem;
`;

export const Spacer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
`;
