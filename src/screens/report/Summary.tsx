 import React from 'react';
import useLayoutAdoptionCheck from '@hooks/useLayoutAdoptionCheck';
import SummaryPageMobile from './Summary.mobile';
import SummaryPageTablet from './Summary.tablet';

const SummaryPage: React.FC = () => {
  const { isTabletMode } = useLayoutAdoptionCheck();

  if (isTabletMode) {
    return <SummaryPageTablet />;
  }

  return <SummaryPageMobile />;
};

export default SummaryPage; 