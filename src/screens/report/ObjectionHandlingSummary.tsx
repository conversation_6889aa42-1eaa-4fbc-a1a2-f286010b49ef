 import React from 'react';
import useLayoutAdoptionCheck from '@hooks/useLayoutAdoptionCheck';
import ObjectionHandlingSummaryMobile from './ObjectionHandlingSummary.mobile';
import ObjectionHandlingSummaryTablet from './ObjectionHandlingSummary.tablet';

const ObjectionHandlingSummary: React.FC = () => {
  const { isTabletMode } = useLayoutAdoptionCheck();

  if (isTabletMode) {
    return <ObjectionHandlingSummaryTablet />;
  }

  return <ObjectionHandlingSummaryMobile />;
};

export default ObjectionHandlingSummary;