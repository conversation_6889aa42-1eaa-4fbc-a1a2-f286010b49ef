import React from "react";
import { useSearchParams } from "react-router-dom";
import { ProductFlowType } from "../../@custom-types/ecoach";
import SummaryPage from "./Summary";
import ObjectionHandlingSummary from "./ObjectionHandlingSummary";
import ProductKnowledgeSummary from "./ProductKnowledgeSummary";

const ReportScreen: React.FC = () => {
  const [searchParams] = useSearchParams();
  const productFlowType = searchParams.get("productFlowType") as ProductFlowType;



  // console.log("ReportScreen productFlowType", productFlowType);
  // Route to appropriate summary type based on productFlowType
  if (productFlowType === ProductFlowType.OBJECTION_HANDLING) {
    return <ObjectionHandlingSummary />;
  } else if (productFlowType === ProductFlowType.QUICKFIRE) {
    return <ProductKnowledgeSummary />;
  }

  // Default to the main Summary page for FULL_EXPERIENCE flow
  return <SummaryPage />;
};

export default ReportScreen
