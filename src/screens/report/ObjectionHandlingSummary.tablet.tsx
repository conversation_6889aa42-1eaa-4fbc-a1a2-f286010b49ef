import React, { useMemo, useState } from 'react';
import styled from 'styled-components';
import { H1, H5, H6, colors, sizes } from '@components/CubeBaseElement';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getConversationData } from '@api/ecoach';
import { extractStringScore } from "@/utils/extractScore";
import StarRating from '@components/StarRating';
import MenuItem from '@components/MenuItem';
import ArrowLeftIcon from '@components/icons/ArrowLeftIcon';
import { XIcon as CloseIcon } from '@components/icons/Close';
import appointmentBG from '@assets/reportBackground2.png'; // Using the same background for tablet
import { ReportLoading } from '@/components/report/ReportLoading';

const PageBGImg = styled.div(() => ({
  flex: 1,
  width: '100%',
  height: '100vh',
  backgroundImage: `url(${appointmentBG})`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  position: 'relative',
}));

const Container = styled.div(() => ({
  flex: 1,
  backgroundColor: 'transparent',
  height: '100%',
}));

const ContentContainer = styled.div(() => ({
  flex: 1,
  height: '100%',
  overflowY: 'auto',
  paddingBottom: '50px',
  display: 'flex',
  flexDirection: 'row', // Tablet layout: side-by-side
  gap: `${sizes[6]}px`,
  padding: `0 ${sizes[8]}px`, // More padding for tablet
}));

const LeftColumn = styled.div(() => ({
  flex: 1,
  maxWidth: '45%',
}));

const RightColumn = styled.div(() => ({
  flex: 1,
  maxWidth: '55%',
}));

const Header = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: `${sizes[6]}px ${sizes[8]}px`, // Larger padding for tablet
  paddingBottom: `${sizes[4]}px`,
}));

const BackBtn = styled.button(() => ({
  padding: `${sizes[5]}px ${sizes[4]}px 0`,
  background: colors.white,
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer',
}));

const OverallScoreContainer = styled.div(() => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  flexDirection: 'column',
  backgroundColor: colors.fwdDarkGreen[100],
  borderRadius: `${sizes[4]}px`,
  padding: `${sizes[8]}px`, // Larger padding for tablet
  marginBottom: `${sizes[6]}px`,
}));

const StarContainer = styled.div(() => ({
  marginBottom: `${sizes[4]}px`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'row',
}));

const ScoreText = styled(H6)(() => ({
  textAlign: 'center',
  marginTop: `${sizes[2]}px`,
  color: colors.white,
}));

const HeartScore = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'baseline',
  justifyContent: 'center',
}));

const CurrentScore = styled(H1)(() => ({
  textAlign: 'center',
  marginRight: `${sizes[1]}px`,
  color: colors.white,
}));

const TotalScore = styled(H5)(() => ({
  textAlign: 'center',
  color: colors.white,
}));

const Title = styled(H5)(() => ({
  marginBottom: `${sizes[4]}px`,
  marginTop: `${sizes[2]}px`,
  color: colors.fwdDarkGreen[100],
}));

const TabContainer = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  margin: '0 5%', // Adjusted margin for tablet
  marginBottom: `${sizes[4]}px`,
}));

const Tab = styled.button<{ $active: boolean }>(({ $active }) => ({
  flex: 1,
  backgroundColor: $active ? colors.alertRedLight : colors.alertGreenLight,
  padding: `${sizes[4]}px 0`, // Larger padding for tablet
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'column',
  borderTopLeftRadius: '8px',
  borderTopRightRadius: '8px',
  border: 'none',
  cursor: 'pointer',
}));

const TabText = styled.div<{ $active: boolean }>(({ $active }) => ({
  color: $active ? colors.alertRed : colors.alertGreen,
  fontSize: '16px', // Larger font for tablet
  fontWeight: 'bold',
}));

const TabNumber = styled(H6)<{ $active: boolean }>(({ $active }) => ({
  color: $active ? colors.alertRed : colors.alertGreen,
  fontSize: '24px', // Larger font for tablet
}));

const SectionContainer = styled.div<{ $active: boolean }>(({ $active }) => ({
  backgroundColor: $active ? '#F2F9F6' : colors.fwdOrange[5],
  marginBottom: `${sizes[4]}px`,
  borderRadius: '4px',
  padding: `${sizes[6]}px`, // Larger padding for tablet
  minHeight: '400px', // Minimum height for tablet
}));

const LoadingContainer = styled.div(() => ({
  flex: 1,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
}));

const ObjectionHandlingSummaryTablet = () => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  
  const conversationId = searchParams.get("conversationId") as string;
  const reportFromState = location.state?.report;
  const session = reportFromState;

  const [activeTab, setActiveTab] = useState<'FEEDBACK' | 'PRAISE'>('FEEDBACK');
  const [summaryInfo, setSummaryInfo] = useState<any>(session);

  const { data, isLoading } = useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData({ conversationId }),
    refetchInterval: summaryInfo ? false : 5000,
    enabled: !session,
  });

  // Handle success case with useEffect
  React.useEffect(() => {
    if (data?.data && !session) {
      setSummaryInfo(data.data);
    }
  }, [data, session]);

  const reportData = session || summaryInfo;

  const displayData = useMemo(() => {
    if (!reportData?.report || !Array.isArray(reportData.report)) return [];
    return reportData.report.filter((item: any) => item.cardType === activeTab);
  }, [activeTab, reportData]);

  const feedbackCount = useMemo(() => {
    if (!reportData?.report || !Array.isArray(reportData.report)) return 0;
    return reportData.report.filter((item: any) => item.cardType === 'FEEDBACK').length;
  }, [reportData]);

  const praiseCount = useMemo(() => {
    if (!reportData?.report || !Array.isArray(reportData.report)) return 0;
    return reportData.report.filter((item: any) => item.cardType === 'PRAISE').length;
  }, [reportData]);

  const handleViewTranscript = () => {
    // This would navigate to a transcript view in the future
    // For now just a placeholder
    console.log('View transcript');
  };

  const exitBtn = () => {
    if (session) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  if (isLoading) {
    return (
      <LoadingContainer>
        <ReportLoading/>
      </LoadingContainer>
    );
  }

  if (!reportData) {
    return (
      <LoadingContainer>
        <ReportLoading/>
      </LoadingContainer>
    );
  }

  const { overall_score } = reportData.report || {};

  return (
    <PageBGImg>
      <Container>
        <Header>
          <BackBtn onClick={exitBtn}>
            {reportData.isFromVideoCall ? (
              <ArrowLeftIcon fill={colors.fwdDarkGreen[100]} />
            ) : (
              <CloseIcon fill={colors.fwdDarkGreen[100]} />
            )}
          </BackBtn>
          <Title fontWeight="bold">{t('myReport')}</Title>
          <div /> {/* Spacer for center alignment */}
        </Header>

        <ContentContainer>
          {/* Left Column - Overall Score */}
          <LeftColumn>
            <OverallScoreContainer>
              <StarContainer>
                <StarRating overallScore={extractStringScore(overall_score)} />
              </StarContainer>
              <ScoreText fontWeight="bold">
                {t('totalScore')}
              </ScoreText>
              <HeartScore>
                <CurrentScore fontWeight="bold">
                  {extractStringScore(overall_score)}
                </CurrentScore>
                <TotalScore fontWeight="bold">
                  / 100
                </TotalScore>
              </HeartScore>
            </OverallScoreContainer>
          </LeftColumn>

          {/* Right Column - Tabs and Content */}
          <RightColumn>
            <TabContainer>
              <Tab $active={activeTab === 'FEEDBACK'} onClick={() => setActiveTab('FEEDBACK')}>
                <TabNumber
                  fontWeight="bold"
                  $active={activeTab === 'FEEDBACK'}>
                  {feedbackCount}
                </TabNumber>
                <TabText $active={activeTab === 'FEEDBACK'}>
                  {t('thingsToImprove')}
                </TabText>
              </Tab>
              <Tab $active={activeTab === 'PRAISE'} onClick={() => setActiveTab('PRAISE')}>
                <TabNumber fontWeight="bold" $active={activeTab === 'PRAISE'}>
                  {praiseCount}
                </TabNumber>
                <TabText $active={activeTab === 'PRAISE'}>
                  {t('thingsWeNailed')}
                </TabText>
              </Tab>
            </TabContainer>

            <SectionContainer $active={activeTab === 'PRAISE'}>
              {displayData.map((item: any, index: number) => (
                <MenuItem
                  active={activeTab === 'PRAISE'}
                  key={index}
                  index={index}
                  title={item.title}
                  observation={item.observation}
                  insight={item.insight}
                  action={item.action}
                />
              ))}
            </SectionContainer>
          </RightColumn>
        </ContentContainer>
      </Container>
    </PageBGImg>
  );
};

export default ObjectionHandlingSummaryTablet;