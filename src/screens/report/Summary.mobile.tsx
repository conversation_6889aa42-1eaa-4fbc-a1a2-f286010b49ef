import React, { useState } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import {H3, H5, H8, Label, SmallLabel, colors, sizes, View, LargeLabel} from '@components/CubeBaseElement';
import { useQuery } from '@tanstack/react-query';
import { getConversationData } from '@api/ecoach';
import { extractStringScore } from "@/utils/extractScore";
import reportBackground from '@assets/reportBackground.png';
import { reportArrowRight, warningFill } from '@assets/index';
import ArrowLeftIcon from '@components/icons/ArrowLeftIcon';
import { XIcon as CloseIcon } from '@components/icons/Close';
import StarRating from "@components/StarRating";
import { ReportLoading } from '@/components/report/ReportLoading';

const HorizontalView = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: `${sizes[1]}px`,
}));

const PageImageBackground = styled.div(() => ({
  flex: 1,
  width: '100%',
  height: '100vh',
  backgroundImage: `url(${reportBackground})`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  position: 'relative',
}));

const YourReportText = styled(H5)(() => ({
  color: colors.fwdDarkGreen[100],
}));

const StarImageContainer = styled.div(() => ({
  display: 'flex',
  alignItems: 'center',
  flexDirection: 'row',
  justifyContent: 'center',
}));

const GreyDot = styled.div(() => ({
  width: '10px',
  height: '10px',
  backgroundColor: colors.fwdDarkGreen[20],
  borderRadius: '5px',
}));

const StarContainer = styled.div`
  margin-top: ${sizes[6]}px;
  width: 100%;
`;

const ScoreText = styled(LargeLabel)(() => ({
  textAlign: 'center',
  marginTop: `${sizes[2]}px`,
  width: '100%',
}));

const HighlightedScoreText = styled(H3)(() => ({
  textAlign: 'center',
}));

const BackBtn = styled.button(() => ({
  paddingTop: `${sizes[5]}px`,
  paddingLeft: `${sizes[4]}px`,
  paddingRight: `${sizes[4]}px`,
  background: 'transparent',
  border: 'none',
  cursor: 'pointer',
}));

const LoadingContainer = styled.div(() => ({
  flex: 1,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
}));

const ScrollContainer = styled.div(() => ({
  margin: `${sizes[4]}px`,
  maxHeight: 'calc(100vh - 100px)',
  overflowY: 'auto',
  paddingBottom: '50px',
  scrollbarWidth: 'none',
}));

const SafeAreaContainer = styled.div(() => ({
  paddingTop: '20px',
  height: '100%',
}));

const Spacer = styled.div<{ height: number }>((props) => ({
  height: `${props.height}px`,
}));

const SummaryCard = styled.div(() => ({
  backgroundColor: colors.white,
  borderRadius: '16px',
  border: "4px solid transparent",
  borderImage: "linear-gradient(127deg, #E9690C 11.93%, #FFC640 33.49%, #1F9AF4 73.6%, #0078CE 100%) 1",
  boxShadow: '0 16px 32px rgba(0, 0, 0, 0.1)',
  padding: `0 ${sizes[4]}px ${sizes[4]}px`,
  marginTop: `${sizes[6]}px`,
  display: 'flex',
  flexDirection: 'column',
}));

const CTAGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
  margin: ${sizes[6]}px 0;
`;

const CTAButton = styled.button<{ variant: 'primary' | 'secondary' }>`
  padding: 16px 24px;
  border-radius: 8px;
  border: ${props => props.variant === 'primary' ? 'none' : `2px solid ${colors.brilBlue[100]}`};
  background-color: ${props => props.variant === 'primary' ? colors.brilBlue[50] : 'transparent'};
  color: ${props => props.variant === 'primary' ? colors.white : colors.fwdDarkGreen[100]};
  font-weight: bold;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
`;
// Simple ScoreContainer component
const SimpleScoreContainer = styled.div<{ noBorderTop?: boolean; noBorderBottom?: boolean }>((props) => ({
  border: '1px solid rgba(133, 157, 153, 0.50)',
  borderTopLeftRadius: props.noBorderTop ? '0' : '4px',
  borderTopRightRadius: props.noBorderTop ? '0' : '4px',
  borderBottomLeftRadius: props.noBorderBottom ? '0' : '4px',
  borderBottomRightRadius: props.noBorderBottom ? '0' : '4px',
  borderBottomColor: props.noBorderBottom ? 'transparent' : 'rgba(133, 157, 153, 0.50)',
  padding: '10px',
  marginBottom: '0',
  backgroundColor: colors.white,
  cursor: 'pointer',
}));
const RowContainer = styled.div({
  display: 'flex',
  flexDirection: 'row',
});

const TitleText = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
  marginBottom: 5,
  flex: 3,
}));

const SeeMoreContainer = styled.div({
  flex: 7,
  display: 'flex',
  flexDirection: 'row',
});

const StatusBarContainer = styled.div({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  flex: 7,
  gap: `${sizes[1]}px`,
});

const StatusBar = styled.div<{ width: number; color: string }>((props) => ({
  borderRadius: '4px',
  backgroundColor: props.color,
  height: `${sizes[7]}px`,
  width: `${props.width}%`,
  minWidth: `${props.width}%`,
  maxWidth: `${props.width}%`,
}));
const SeeMoreText = styled(SmallLabel)(() => ({
  textAlign: 'left',
  color: colors.fwdOrange[100],
  marginTop: sizes[1],
}));

const StyledImage = styled.img({
  width: sizes[3],
  height: sizes[3],
  marginLeft: sizes[2],
  marginTop: sizes[1],
  objectFit: 'contain',
});

interface ScoreContainerProps {
  title: string;
  score: number;
  skillSet?: any;
  color: string;
  noBorderTop?: boolean;
  noBorderBottom?: boolean;
}

const ScoreContainer: React.FC<ScoreContainerProps> = ({
  title,
  score,
  skillSet,
  color,
  noBorderTop,
  noBorderBottom,
}) => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const goDetailSummaryPage = () => {
    navigate(`/report/details?${searchParams.toString()}&skill=${encodeURIComponent(title)}`, {
      state: { skillSet }
    });
  };

  const getStatusBarProps = () => ({
    width: score,
    color: score < 50 ? color : colors.fwdBlue[50],
  });
  return (
    <SimpleScoreContainer
      noBorderTop={noBorderTop}
      noBorderBottom={noBorderBottom}
      onClick={goDetailSummaryPage}
    >
      <RowContainer>
        <TitleText fontWeight={'bold'}>{title}</TitleText>
        <StatusBarContainer>
          <StatusBar {...getStatusBarProps()} />
          <ScoreText fontWeight={'bold'}>{score}</ScoreText>
          <View style={{ width: `${90 - score}%` }} />
        </StatusBarContainer>
      </RowContainer>
      <RowContainer>
        <TitleText>{}</TitleText>
        <SeeMoreContainer>
          <View style={{ flexDirection: 'row', flex: 9 }}>
            {score < 50 ? (
                <SeeMoreText fontWeight={'bold'}>
                  {t('howYouCanDoBetter')}
                </SeeMoreText>
            ) : (
                <SeeMoreText fontWeight={'bold'}>
                  {t('seeMoreDetails')}
                </SeeMoreText>
            )}
            {score < 50 && (
                <StyledImage src={warningFill} alt="warning" />
            )}
          </View>
          <View style={{ flex: 1, flexDirection: 'row' }}>
            <StyledImage src={reportArrowRight} alt="arrow right" />
          </View>
        </SeeMoreContainer>
      </RowContainer>
    </SimpleScoreContainer>
  );
};

interface SummaryPageProps {}

const SummaryPage: React.FC<SummaryPageProps> = () => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  
  const conversationId = searchParams.get("conversationId") as string;
  
  // Use report from state if available
  const session = location.state?.report;
  
  const [summaryInfo, setSummaryInfo] = useState<any>(session);
  const [showFeedbackModal, setShowFeedbackModal] = useState<boolean>(!session);

  const { data, isLoading, error } = useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData({ conversationId }),
    refetchInterval: summaryInfo ? false : 5000,
    enabled: !session,
  });

  // Handle success case with useEffect (replaces deprecated onSuccess)
  React.useEffect(() => {
    if (data?.data?.report_is_ready === 'true' && !session) {
      setSummaryInfo(data.data);
    }
  }, [data, session]);

  // Handle error case with useEffect (replaces deprecated onError)
  React.useEffect(() => {
    if (error) {
      console.log('SummaryPage getConversationData error', JSON.stringify(error));
    }
  }, [error]);

  const reportData = session || summaryInfo;

  const tryAgain = () => {
    const urlParams = new URLSearchParams(searchParams.toString());
    urlParams.delete("conversationId");
    urlParams.delete("ref");
    navigate(`/`);
  };

  const goBackBtn = () => {
    if (session) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  if (isLoading) {
    return (
      <LoadingContainer>
          <ReportLoading/>
      </LoadingContainer>
    );
  }

  if (!reportData) {
    return (
      <LoadingContainer>
          <ReportLoading/>
      </LoadingContainer>
    );
  }

  const { report } = reportData || {};

  const {
    overall_score,
    customer_relationship_score,
    skill_set_details,
    customer_discovery_score,
    applied_product_knowledge_score,
    objection_handling_closing_score,
    communication_skills_score,
  } = report || {};

  return (
    <PageImageBackground>
      <SafeAreaContainer>
        <BackBtn onClick={goBackBtn}>
          {session ? (
            <ArrowLeftIcon fill={colors.fwdDarkGreen[100]} size={24} />
          ) : (
            <CloseIcon fill={colors.fwdDarkGreen[100]} />
          )}
        </BackBtn>
        <ScrollContainer>
          <HorizontalView>
            <YourReportText fontWeight="bold">
              {t('yourReport')}
            </YourReportText>
          </HorizontalView>
          {overall_score && skill_set_details && (
            <SummaryCard>
              <StarImageContainer>
                <GreyDot />
                <StarContainer>
                  <StarRating overallScore={Number(overall_score)} size={24} />
                </StarContainer>
                <GreyDot />
              </StarImageContainer>
              <ScoreText fontWeight="bold" color={colors.fwdGreyDarkest}>
                {t('overallScore')}
              </ScoreText>
              <Label
                fontWeight="bold"
                color={colors.fwdDarkGreen[50]}
                style={{ textAlign: 'center' }}>
                <HighlightedScoreText
                  fontWeight="bold"
                  color={colors.fwdDarkGreen[100]}>
                  {extractStringScore(overall_score)}{' '}
                </HighlightedScoreText>
                / 100
              </Label>
              <Spacer height={sizes[4]} />
              {customer_relationship_score && (
                <ScoreContainer
                  title={t('relationshipBuilding')}
                  score={extractStringScore(customer_relationship_score)}
                  skillSet={skill_set_details?.customer_relationship}
                  color={colors.fwdLightGreen[100]}
                  noBorderBottom={
                    !!customer_discovery_score ||
                    !!applied_product_knowledge_score ||
                    !!objection_handling_closing_score ||
                    !!communication_skills_score
                  }
                />
              )}
              {customer_discovery_score && (
                <ScoreContainer
                  title={t('customerDiscovery')}
                  score={extractStringScore(customer_discovery_score)}
                  skillSet={skill_set_details?.customer_discovery}
                  color={colors.fwdBlue[20]}
                  noBorderTop={!!customer_relationship_score}
                  noBorderBottom={
                    !!applied_product_knowledge_score ||
                    !!objection_handling_closing_score ||
                    !!communication_skills_score
                  }
                />
              )}
              {applied_product_knowledge_score && (
                <ScoreContainer
                  title={t('productKnowledge')}
                  score={extractStringScore(applied_product_knowledge_score)}
                  skillSet={skill_set_details?.applied_product_knowledge}
                  color={colors.fwdLightGreen[50]}
                  noBorderTop={
                    !!customer_discovery_score ||
                    !!customer_relationship_score
                  }
                  noBorderBottom={
                    !!objection_handling_closing_score ||
                    !!communication_skills_score
                  }
                />
              )}
              {objection_handling_closing_score && (
                <ScoreContainer
                  title={t('objectionsAndClosing')}
                  score={extractStringScore(objection_handling_closing_score)}
                  skillSet={skill_set_details?.objection_handling_closing}
                  color={colors.fwdBlue[100]}
                  noBorderTop={
                    !!customer_discovery_score ||
                    !!applied_product_knowledge_score ||
                    !!customer_relationship_score
                  }
                  noBorderBottom={!!communication_skills_score}
                />
              )}
              {communication_skills_score && (
                <ScoreContainer
                  title={t('speechAndAnalysis')}
                  score={extractStringScore(communication_skills_score)}
                  skillSet={skill_set_details?.communication_skills}
                  color={colors.fwdBlue[50]}
                  noBorderTop={
                    !!customer_discovery_score ||
                    !!applied_product_knowledge_score ||
                    !!objection_handling_closing_score ||
                    !!customer_relationship_score
                  }
                />
              )}
              <Spacer height={sizes[3]} />
              <HorizontalView>
                <GreyDot />
                <GreyDot />
              </HorizontalView>
            </SummaryCard>
          )}
          <Spacer height={sizes[4]} />
          {!session && (
            <CTAGroup>
              <CTAButton
                variant="primary"
                onClick={tryAgain}
              >
                {t('letsTryAgain')}
              </CTAButton>
              <CTAButton
                variant="secondary"
                onClick={() => {
                  navigate('/');
                }}
              >
                {t('exit')}
              </CTAButton>
            </CTAGroup>
          )}
        </ScrollContainer>
      </SafeAreaContainer>
    </PageImageBackground>
  );
};

export default SummaryPage; 