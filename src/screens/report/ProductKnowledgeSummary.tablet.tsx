import React, { useState } from 'react';
import styled from 'styled-components';
import { H1, H5, H6, H7, Label, LargeBody, colors, sizes } from '@components/CubeBaseElement';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getConversationData } from '@api/ecoach';
import StarRating from '@components/StarRating';
import { XIcon as CloseIcon } from '@components/icons/Close';
import { ProductFlowType } from '../../@custom-types/ecoach';
import appointmentBG from '@assets/reportBackground2.png'; // Using the same background for tablet
import { ReportLoading } from '@/components/report/ReportLoading';

// Custom SVG Icons for correct/incorrect answers
const CheckIcon = () => (
  <svg width={sizes[5]} height={sizes[5]} viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill={colors.alertGreen}/>
    <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const XIcon = () => (
  <svg width={sizes[5]} height={sizes[5]} viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill={colors.alertRed}/>
    <path d="M15 9l-6 6m0-6l6 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const PageBGImg = styled.div(() => ({
  flex: 1,
  width: '100%',
  height: '100vh',
  backgroundImage: `url(${appointmentBG})`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  position: 'relative',
}));

const BackgroundBottomCover = styled.div(() => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  backgroundColor: 'transparent',
  height: '20%',
}));

const Container = styled.div(() => ({
  flex: 1,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  padding: `${sizes[4]}px`,
}));

const ScreenContainer = styled.div(() => ({
  flex: 1,
  height: '100%',
  display: 'flex',
  flexDirection: 'row', // Tablet layout: side-by-side
  gap: `${sizes[8]}px`, // Larger gap for tablet
  maxWidth: '1200px', // Maximum width for tablet
}));

const LeftColumn = styled.div(() => ({
  flex: 1,
  maxWidth: '40%',
}));

const RightColumn = styled.div(() => ({
  flex: 1,
  maxWidth: '60%',
  overflowY: 'auto',
  paddingRight: `${sizes[4]}px`,
}));

const BackBtn = styled.button(() => ({
  position: 'absolute',
  top: `${sizes[4]}px`,
  right: `${sizes[4]}px`,
  background: 'transparent',
  border: 'none',
  cursor: 'pointer',
  zIndex: 10,
}));

const Header = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: `${sizes[6]}px`,
}));

const OverallScoreContainer = styled.div(() => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  flexDirection: 'column',
  backgroundColor: colors.fwdDarkGreen[100],
  borderRadius: `${sizes[4]}px`,
  padding: `${sizes[8]}px`, // Larger padding for tablet
  marginBottom: `${sizes[6]}px`,
}));

const StarContainer = styled.div(() => ({
  marginBottom: `${sizes[4]}px`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'row',
}));

const ScoreText = styled(H6)(() => ({
  textAlign: 'center',
  marginTop: `${sizes[2]}px`,
  color: colors.white,
}));

const HeartScore = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'baseline',
  justifyContent: 'center',
}));

const CurrentScore = styled(H1)(() => ({
  textAlign: 'center',
  marginRight: `${sizes[1]}px`,
  color: colors.white,
}));

const TotalScore = styled(H5)(() => ({
  textAlign: 'center',
  color: colors.white,
}));

const Title = styled(H5)(() => ({
  color: colors.fwdDarkGreen[100],
  marginBottom: `${sizes[4]}px`,
}));

const ConvoIDText = styled(Label)(() => ({
  marginTop: `${sizes[6]}px`,
  textAlign: 'center',
  color: colors.fwdDarkGreen[50],
}));

const CTAGroup = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  gap: `${sizes[3]}px`,
  marginTop: `${sizes[6]}px`,
  marginBottom: `${sizes[4]}px`,
}));

const CTAButton = styled.button<{ variant: 'primary' | 'secondary' }>((props) => ({
  flex: 1,
  padding: `${sizes[3]}px ${sizes[4]}px`,
  borderRadius: `${sizes[2]}px`,
  border: props.variant === 'primary' ? 'none' : `2px solid ${colors.fwdDarkGreen[100]}`,
  backgroundColor: props.variant === 'primary' ? colors.alertGreen : 'transparent',
  color: props.variant === 'primary' ? colors.white : colors.fwdDarkGreen[100],
  fontSize: '16px', // Larger font for tablet
  fontWeight: 'bold',
  cursor: 'pointer',
  '&:hover': {
    opacity: 0.8,
  },
}));

const QuestionItemContainer = styled.div(() => ({
  backgroundColor: colors.white,
  borderRadius: `${sizes[2]}px`,
  padding: `${sizes[6]}px`, // Larger padding for tablet
  marginBottom: `${sizes[4]}px`,
  border: `1px solid ${colors.fwdDarkGreen[20]}`,
}));

const QuestionWrapper = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'flex-start',
  marginBottom: `${sizes[3]}px`,
  gap: `${sizes[2]}px`,
}));

const AnswerWrapper = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'flex-start',
  gap: `${sizes[2]}px`,
  marginBottom: `${sizes[3]}px`,
}));

const AnswerContent = styled.div(() => ({
  flex: 1,
}));

const ReasoningWrapper = styled.div(() => ({
  marginTop: `${sizes[3]}px`,
  padding: `${sizes[3]}px`,
  backgroundColor: colors.fwdOrange[5],
  borderRadius: `${sizes[1]}px`,
}));

const LoadingContainer = styled.div(() => ({
  flex: 1,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
}));

// QuestionItem component
interface QuestionItemProps {
  item: any;
  index: number;
}

const QuestionItem: React.FC<QuestionItemProps> = ({ item, index }) => {
  const { t } = useTranslation('ecoach');
  
  return (
    <QuestionItemContainer>
      <QuestionWrapper>
        <H6 fontWeight="medium">{index + 1}.</H6>
        <H6 fontWeight="medium" style={{ flex: 1 }}>
          {item.text}
        </H6>
      </QuestionWrapper>
      <AnswerWrapper>
        {item.answered_correctly === 'Yes' ? (
          <CheckIcon />
        ) : (
          <XIcon />
        )}
        <AnswerContent>
          <LargeBody>{item.your_answer}</LargeBody>
        </AnswerContent>
      </AnswerWrapper>
      {item.answered_correctly !== 'Yes' && item.reasoning && (
        <ReasoningWrapper>
          <H7 fontWeight="bold">{t('explanation')}</H7>
          <LargeBody>{item.reasoning}</LargeBody>
        </ReasoningWrapper>
      )}
    </QuestionItemContainer>
  );
};

const ProductKnowledgeSummaryTablet = () => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const conversationId = searchParams.get("conversationId") as string;
  const session = location.state?.report;

  const [summaryInfo, setSummaryInfo] = useState<any>(session);

  const { data, isLoading } = useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData({ conversationId }),
    refetchInterval: summaryInfo ? false : 5000,
    enabled: !session,
  });

  // Handle success case with useEffect
  React.useEffect(() => {
    if (data?.data && !session) {
      setSummaryInfo(data.data);
    }
  }, [data, session]);

  const reportData = session || summaryInfo;

  const exitBtn = () => {
    if (session) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  if (isLoading) {
    return (
      <LoadingContainer>
        <ReportLoading/>
      </LoadingContainer>
    );
  }

  if (!reportData) {
    return (
      <LoadingContainer>
        <ReportLoading/>
      </LoadingContainer>
    );
  }

  const { report } = reportData || {};
  const { questions } = report || {};

  const correctAnswers = questions?.filter(
    (item: any) => item.answered_correctly === 'Yes',
  ).length || 0;

  const totalQuestions = questions?.length || 0;
  const overallScore = Math.round((correctAnswers / totalQuestions) * 100) || 0;

  return (
    <PageBGImg>
      <BackgroundBottomCover />
      <Container>
        <ScreenContainer>
          {/* Left Column - Score and Actions */}
          <LeftColumn>
            <Header>
              <Title fontWeight="bold">{t('yourReport')}</Title>
              <BackBtn onClick={exitBtn}>
                <CloseIcon fill={colors.fwdDarkGreen[100]} />
              </BackBtn>
            </Header>

            <OverallScoreContainer>
              <StarContainer>
                <StarRating overallScore={overallScore} />
              </StarContainer>
              <ScoreText>
                {t('yourScore')}
              </ScoreText>
              <HeartScore>
                <CurrentScore fontWeight="bold">
                  {correctAnswers}
                </CurrentScore>
                <TotalScore fontWeight="bold">
                  / {totalQuestions}
                </TotalScore>
              </HeartScore>
            </OverallScoreContainer>

            <CTAGroup>
              <CTAButton
                variant="primary"
                onClick={() => {
                  const urlParams = new URLSearchParams(searchParams.toString());
                  urlParams.set("productFlowType", ProductFlowType.QUICKFIRE);
                  navigate(`/`);
                }}
              >
                {t('letsTryAgain')}
              </CTAButton>
              <CTAButton
                variant="secondary"
                onClick={() => {
                  navigate('/');
                }}
              >
                {t('exit')}
              </CTAButton>
            </CTAGroup>

            <ConvoIDText>
              Conversation ID: {summaryInfo.conversation_id}
            </ConvoIDText>
          </LeftColumn>

          {/* Right Column - Questions */}
          <RightColumn>
            {questions?.map((item: any, index: number) => (
              <QuestionItem key={index} item={item} index={index} />
            ))}
          </RightColumn>
        </ScreenContainer>
      </Container>
    </PageBGImg>
  );
};

export default ProductKnowledgeSummaryTablet; 