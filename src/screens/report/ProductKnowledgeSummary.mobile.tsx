import React, { FC, useState } from 'react';
import styled from 'styled-components';
import {H1, H5, H6, H7, Label, LargeBody, colors, sizes, LargeLabel} from '@components/CubeBaseElement';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getConversationData } from '@api/ecoach';
import StarRating from '@components/StarRating';
import { XIcon as CloseIcon } from '@components/icons/Close';
import { ProductFlowType } from '../../@custom-types/ecoach';
import appointmentBG from '@assets/mAppointmentRPBG.png';
import ArrowLeftIcon from "@components/icons/ArrowLeftIcon"; // Using the same background for now
import { ReportLoading } from '@/components/report/ReportLoading';
// Simple icon components using SVG
const CheckIcon = () => (
  <svg width={sizes[5]} height={sizes[5]} viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill={colors.alertGreen}/>
    <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const XIcon = () => (
  <svg width={sizes[5]} height={sizes[5]} viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill={colors.alertRed}/>
    <path d="M15 9l-6 6m0-6l6 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const MAX_SCORE = 100;

export type QuestionAndAssessment = {
  question: string;
  answer: string;
  answered_correctly: string;
  reasoning?: string;
};

const PageBGImg = styled.div(() => ({
  flex: 1,
  width: '100%',
  height: '100vh',
  backgroundImage: `url(${appointmentBG})`,
  backgroundSize: 'cover',
  backgroundRepeat: 'no-repeat',
  position: 'relative',
  marginTop: `-${sizes[8]}px`,
  paddingTop: '40px',
  marginBottom: `-${sizes[12]}px`,
}));

const Container = styled.div(() => ({
  flex: 1,
  backgroundColor: 'transparent',
  zIndex: 2,
  height: '100%',
}));

const LoadingContainer = styled.div(() => ({
  flex: 1,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
}));

const ScreenContainer = styled.div(() => ({
  flex: 1,
  height: '100%',
}));

const ContentContainer = styled.div(() => ({
  flex: 1,
  paddingBottom: `${sizes[16]}px`,
  marginBottom: `${sizes[16]}px`,
  padding: `0 ${sizes[4]}px`,
  overflowY: 'auto',
  maxHeight: 'calc(100vh - 100px)',
}));

const BackBtn = styled.button(() => ({
  background: 'transparent',
  border: 'none',
  cursor: 'pointer',
}));

const Header = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: `${sizes[4]}px`,
  paddingBottom: `${sizes[2]}px`,
}));

const Title = styled(H5)`
  margin-bottom: ${sizes[4]}px;
  margin-top: ${sizes[2]}px;
  color: ${colors.fwdDarkGreen[100]};
`;

const OverallScoreContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
`;

const StarContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  transform: scale(1.5);
  margin-bottom: ${sizes[2]}px;
  margin-top: ${sizes[6]}px;
`;

const ScoreText = styled(LargeLabel)`
  text-align: center;
  margin-top: ${sizes[2]}px;
`;

const HeartScore = styled.div`
  display: flex;
  flex-direction: row;
  align-items: baseline;
  justify-content: center;
`;

const CurrentScore = styled(H1)`
  text-align: center;
  margin-right: ${sizes[1]}px;
`;

const TotalScore = styled(H5)`
  text-align: center;
`;

const QuestionItemContainer = styled.div`
  display: flex;
  flex-direction: column;
  background-color: ${colors.white};
  border-radius: ${sizes[4]}px;
  padding: ${sizes[4]}px;
  border: 2px solid ${colors.fwdGrey[100]};
  gap: ${sizes[4]}px;
  overflow: hidden;
  margin-bottom: ${sizes[5]}px;
`;

const QuestionWrapper = styled.div`
  display: flex;
  flex-direction: row;
  gap: ${sizes[2]}px;
  width: 100%;
  overflow: hidden;
`;

const AnswerWrapper = styled.div`
  display: flex;
  flex-direction: row;
  gap: ${sizes[2]}px;
  background-color: ${colors.fwdGrey[50]};
  padding: ${sizes[3]}px;
  border-radius: ${sizes[2]}px;
  align-items: flex-start;
`;

const AnswerContent = styled(Label)`
  flex: 1;
  color: ${colors.fwdGreyDarker};
  margin-top: 2px;
`;

const ReasoningWrapper = styled.div`
  gap: ${sizes[2]}px;
  display: flex;
  flex-direction: column;
`;

const CTAGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
  margin: ${sizes[6]}px 0;
`;

const CTAButton = styled.button<{ variant: 'primary' | 'secondary' }>`
  padding: 16px 24px;
  border-radius: 8px;
  border: ${props => props.variant === 'primary' ? 'none' : `2px solid ${colors.brilBlue[100]}`};
  background-color: ${props => props.variant === 'primary' ? colors.brilBlue[50] : 'transparent'};
  color: ${props => props.variant === 'primary' ? colors.white : colors.fwdDarkGreen[100]};
  font-weight: bold;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
`;

const BackgroundBottomCover = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: linear-gradient(transparent, rgba(255, 255, 255, 0.8));
  z-index: 1;
`;

const ProductKnowledgeSummary = () => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  
  const conversationId = searchParams.get("conversationId") as string;
  const session = location.state?.report;

  const [summaryInfo, setSummaryInfo] = useState<any>(session);

  const { data, isLoading } = useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData({ conversationId }),
    refetchInterval: summaryInfo ? false : 5000,
    enabled: !session,
  });



  // Handle success case with useEffect
  React.useEffect(() => {
    if (data?.data && !session) {
      setSummaryInfo(data.data);
    }
  }, [data, session]);
  console.log("ProductKnowledgeSummary summaryInfo", summaryInfo )
  const reportData = session || summaryInfo;

  const exitBtn = () => {
    if (session) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  // Show loading page if data is still loading
  if (isLoading) {
    return (
      <LoadingContainer>
        <ReportLoading/>
      </LoadingContainer>
    );
  }

  if (!reportData) {
    return (
      <LoadingContainer>
        <ReportLoading/>
      </LoadingContainer>
    );
  }

  const questions_and_assessments = reportData.report?.questions_and_assessments || [];

  const answeredCorrectlyCount = questions_and_assessments.filter(
    (item: any) => item.answered_correctly === 'Yes',
  ).length;
  
  const overallScore = questions_and_assessments.length > 0 
    ? Math.round((answeredCorrectlyCount / questions_and_assessments.length) * MAX_SCORE)
    : 0;

  return (
    <PageBGImg>
      <BackgroundBottomCover />
      <Container>
        <ScreenContainer style={{ marginTop: '20px' }}>
          <Header>
            <BackBtn onClick={exitBtn}>
              {session ? (
                <ArrowLeftIcon fill={colors.fwdDarkGreen[100]} size={24} />
              ) : (
                <CloseIcon fill={colors.fwdDarkGreen[100]} />
              )}
            </BackBtn>
          </Header>
          <ContentContainer>
            <Title fontWeight="bold">{t('yourReport')}</Title>
            <OverallScoreContainer>
              <StarContainer>
                <StarRating overallScore={overallScore} />
              </StarContainer>
              <ScoreText color={colors.fwdGreyDarkest} fontWeight={"500"}>
                {t('yourScore')}
              </ScoreText>
              <HeartScore>
                <CurrentScore color={colors.black} fontWeight="bold">
                  {overallScore}
                </CurrentScore>
                <TotalScore color={colors.fwdDarkGreen[50]} fontWeight="bold">
                  /100
                </TotalScore>
              </HeartScore>
            </OverallScoreContainer>
            {questions_and_assessments.map((item: any, index: number) => (
              <QuestionItem key={index} item={item} index={index} />
            ))}
            {!session && (
              <CTAGroup>
                <CTAButton
                  variant="primary"
                  onClick={() => {
                    const urlParams = new URLSearchParams(searchParams.toString());
                    urlParams.set("productFlowType", ProductFlowType.QUICKFIRE);
                    navigate(`/`);
                  }}
                >
                  {t('letsTryAgain')}
                </CTAButton>
                <CTAButton
                  variant="secondary"
                  onClick={() => {
                    navigate('/');
                  }}
                >
                  {t('exit')}
                </CTAButton>
              </CTAGroup>
            )}
          </ContentContainer>
        </ScreenContainer>
      </Container>
    </PageBGImg>
  );
};

export type QuestionItemProps = { item: QuestionAndAssessment; index: number };

export const QuestionItem: FC<QuestionItemProps> = ({ item, index }) => {
  const { t } = useTranslation('ecoach');
  
  return (
    <QuestionItemContainer>
      <QuestionWrapper>
        <H6 fontWeight="500">{index + 1}.</H6>
        <H6 fontWeight="500" style={{ flex: 1 }}>
          {item.question}
        </H6>
      </QuestionWrapper>
      <AnswerWrapper>
        {item.answered_correctly === 'Yes' ? (
          <CheckIcon />
        ) : (
          <XIcon />
        )}
        <AnswerContent>
          {item.answer}
        </AnswerContent>
      </AnswerWrapper>
      {item.answered_correctly !== 'Yes' && item.reasoning && (
        <ReasoningWrapper>
          <H7 fontWeight="bold">{t('explanation')}</H7>
          <LargeBody>{item.reasoning}</LargeBody>
        </ReasoningWrapper>
      )}
    </QuestionItemContainer>
  );
};

export default ProductKnowledgeSummary; 