 import React from 'react';
import useLayoutAdoptionCheck from '@hooks/useLayoutAdoptionCheck';
import ProductKnowledgeSummaryMobile from './ProductKnowledgeSummary.mobile';
import ProductKnowledgeSummaryTablet from './ProductKnowledgeSummary.tablet';

const ProductKnowledgeSummary: React.FC = () => {
  const { isTabletMode } = useLayoutAdoptionCheck();

  if (isTabletMode) {
    return <ProductKnowledgeSummaryTablet />;
  }

  return <ProductKnowledgeSummaryMobile />;
};

export default ProductKnowledgeSummary; 