import React, { useMemo, useState } from 'react';
import styled from 'styled-components';
import {H1, H5, H6, colors, sizes, H7} from '@components/CubeBaseElement';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getConversationData } from '@api/ecoach';
import { extractStringScore } from "@/utils/extractScore";
import StarRating from '@components/StarRating';
import MenuItem from '@components/MenuItem';
import ArrowLeftIcon from '@components/icons/ArrowLeftIcon';
import { XIcon as CloseIcon } from '@components/icons/Close';
import appointmentBG from '@assets/mAppointmentRPBG.png';
import {ProductFlowType} from "@/@custom-types/ecoach"; // Using the same background for now
import { ReportLoading } from '@/components/report/ReportLoading';

const PageBGImg = styled.div(() => ({
  flex: 1,
  width: '100%',
  height: '100vh',
  backgroundImage: `url(${appointmentBG})`,
  backgroundSize: 'cover',
  backgroundRepeat: 'no-repeat',
  position: 'relative',
  marginTop: `-${sizes[8]}px`,
  paddingTop: '40px',
  marginBottom: `-${sizes[12]}px`,
}));

const Container = styled.div(() => ({
  flex: 1,
  backgroundColor: 'transparent',
  height: '100%',
}));

const ContentContainer = styled.div(() => ({
  flex: 1,
  height: '100%',
  overflowY: 'auto',
  paddingBottom: '50px',
}));

const Header = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingLeft: `${sizes[4]}px`,
  paddingRight: `${sizes[4]}px`,
}));

const BackBtn = styled.button(() => ({
  padding: `${sizes[4]}px ${sizes[4]}px 0`,
  background: colors.white,
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer',
}));

const OverallScoreContainer = styled.div(() => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  flexDirection: 'column',
}));

const StarContainer = styled.div(() => ({
  marginBottom: `${sizes[4]}px`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'row',
}));

const ScoreText = styled(H7)(() => ({
  textAlign: 'center',
}));

const HeartScore = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'baseline',
  justifyContent: 'center',
}));

const CurrentScore = styled(H1)(() => ({
  textAlign: 'center',
  marginRight: `${sizes[1]}px`,
}));

const TotalScore = styled(H5)(() => ({
  textAlign: 'center',
}));

const Title = styled(H5)(() => ({
  marginBottom: `${sizes[4]}px`,
  marginTop: `${sizes[2]}px`,
  color: colors.fwdDarkGreen[100],
}));

const TabContainer = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  margin: '0 10%',
}));

const Tab = styled.button<{ $active: boolean }>(({ $active }) => ({
  flex: 1,
  backgroundColor: $active ? colors.alertRedLight : "#F7FAFF",
  padding: `${sizes[3]}px 0`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'column',
  borderTopLeftRadius: '16px',
  borderTopRightRadius: '16px',
  border: 'none',
  cursor: 'pointer',
}));

const TabText = styled.div<{ $active: boolean }>(({ $active }) => ({
  color: $active ? colors.alertRed : "#0178CD",
  fontSize: '14px',
}));

const TabNumber = styled(H6)<{ $active: boolean }>(({ $active }) => ({
  color: $active ? colors.alertRed : "#0178CD",
}));

const SectionContainer = styled.div<{ $active: boolean }>(({ $active }) => ({
  backgroundColor: $active ? '#F2F9F6' : colors.fwdOrange[5],
  marginBottom: `${sizes[4]}px`,
  borderRadius: '4px',
  padding: `${sizes[4]}px`,
  display: 'flex',
  flexDirection: 'column',
}));

const CTAGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
  margin: ${sizes[6]}px 0;
  padding: 0 ${sizes[4]}px;
`;

const CTAButton = styled.button<{ variant: 'primary' | 'secondary' }>`
  padding: 16px 24px;
  border-radius: 8px;
  border: ${props => props.variant === 'primary' ? 'none' : `2px solid ${colors.brilBlue[100]}`};
  background-color: ${props => props.variant === 'primary' ? colors.brilBlue[50] : 'transparent'};
  color: ${props => props.variant === 'primary' ? colors.white : colors.fwdDarkGreen[100]};
  font-weight: bold;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
`;

const LoadingContainer = styled.div(() => ({
  flex: 1,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
}));

const ObjectionHandlingSummaryMobile = () => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  
  const conversationId = searchParams.get("conversationId") as string;
  const session = location.state?.report;

  const [activeTab, setActiveTab] = useState<'FEEDBACK' | 'PRAISE'>('FEEDBACK');
  const [summaryInfo, setSummaryInfo] = useState<any>(session);

  const { data, isLoading } = useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData({ conversationId }),
    refetchInterval: summaryInfo ? false : 5000,
    enabled: !session,
  });

  // Handle success case with useEffect
  React.useEffect(() => {
    if (data?.data && !session) {
      setSummaryInfo(data.data);
    }
  }, [data, session]);

  const reportData = session || summaryInfo;
  const displayData = useMemo(() => {
    if (!reportData?.report || !Array.isArray(reportData.report)) return [];
    return reportData.report.filter((item: any) => item.cardType === activeTab);
  }, [activeTab, reportData]);

  const feedbackCount = useMemo(() => {
    if (!reportData?.report || !Array.isArray(reportData.report)) return 0;
    return reportData.report.filter((item: any) => item.cardType === 'FEEDBACK').length;
  }, [reportData]);

  const praiseCount = useMemo(() => {
    if (!reportData?.report || !Array.isArray(reportData.report)) return 0;
    return reportData.report.filter((item: any) => item.cardType === 'PRAISE').length;
  }, [reportData]);

  const handleViewTranscript = () => {
    // This would navigate to a transcript view in the future
    // For now just a placeholder
    console.log('View transcript');
  };

  const exitBtn = () => {
    if (session) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  // Show loading page if data is still loading
  if (isLoading) {
    return (
      <LoadingContainer>
        <ReportLoading/>
      </LoadingContainer>
    );
  }

  if (!reportData) {
    return (
      <LoadingContainer>
        <ReportLoading/>
      </LoadingContainer>
    );
  }

  // For this new API response format, we'll use a default score of 0 since
  // the overall_score is not provided in the new format
  const overall_score = reportData.score?.toString() || '0';

  return (
    <PageBGImg>
      <Container>
        <ContentContainer style={{ marginTop: '20px' }}>
          <BackBtn onClick={exitBtn}>
            {session ? (
                <ArrowLeftIcon fill={colors.fwdDarkGreen[100]} size={24} />
            ) : (
                <CloseIcon fill={colors.fwdDarkGreen[100]} />
            )}
          </BackBtn>
          <Header>
            <Title fontWeight="bold">{t('myReport')}</Title>
          </Header>
          <OverallScoreContainer>
            <StarContainer>
              <StarRating overallScore={extractStringScore(overall_score)} />
            </StarContainer>
              <ScoreText fontWeight="500" color={colors.fwdGreyDarkest}>
                {t('overallScore')}
              </ScoreText>
            <HeartScore>
              <CurrentScore color={colors.black} fontWeight="bold">
                {extractStringScore(overall_score)}
              </CurrentScore>
              <TotalScore color={colors.fwdDarkGreen[50]} fontWeight="bold">
                /100
              </TotalScore>
            </HeartScore>
          </OverallScoreContainer>

          <TabContainer>
            <Tab $active={true} onClick={() => setActiveTab('FEEDBACK')}>
              <TabNumber
                fontWeight="bold"
                $active={true}>
                {feedbackCount}
              </TabNumber>
              <TabText $active={true}>
                {t('thingsToImprove')}
              </TabText>
            </Tab>
            <Tab $active={false} onClick={() => setActiveTab('PRAISE')}>
              <TabNumber fontWeight="bold" $active={false}>
                {praiseCount}
              </TabNumber>
              <TabText $active={false}>
                {t('thingsWeNailed')}
              </TabText>
            </Tab>
          </TabContainer>

          <SectionContainer $active={activeTab === 'PRAISE'}>
            {displayData.map((item: any, index: number) => (
              <MenuItem
                active={activeTab === 'PRAISE'}
                key={index}
                index={index}
                title={item.title}
                observation={item.observation}
                insight={item.insight}
                action={item.action}
              />
            ))}
          </SectionContainer>
          {!session && (
            <CTAGroup>
              <CTAButton
                variant="primary"
                onClick={() => {
                  const urlParams = new URLSearchParams(searchParams.toString());
                  urlParams.set("productFlowType", ProductFlowType.OBJECTION_HANDLING);
                  navigate(`/`);
                }}
              >
                {t('letsTryAgain')}
              </CTAButton>
              <CTAButton
                variant="secondary"
                onClick={() => {
                  navigate('/');
                }}
              >
                {t('exit')}
              </CTAButton>
            </CTAGroup>
          )}
        </ContentContainer>
      </Container>
    </PageBGImg>
  );
};

export default ObjectionHandlingSummaryMobile;