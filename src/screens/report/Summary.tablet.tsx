import React, { useState } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { H3, H5, H8, Label, SmallLabel, colors, sizes } from '@components/CubeBaseElement';
import { useQuery } from '@tanstack/react-query';
import { getConversationData } from '@api/ecoach';
import {extractStringScore} from "@/utils/extractScore";

import dayjs from 'dayjs';
import reportBackground from '@assets/reportBackground.png'; // Different background for tablet

import {
  emptyStar,
  halfStar,
  oneHalfStar,
  oneStar,
  threeStar,
  twoHalfStar,
  twoStar,
} from '@assets/index';

import ArrowLeftIcon from '@components/icons/ArrowLeftIcon';
import { XIcon as CloseIcon } from '@components/icons/Close';
import { ReportLoading } from '@/components/report/ReportLoading';

const HorizontalView = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: `${sizes[1]}px`,
}));

const BtnGroupView = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginLeft: '360px', // Tablet-specific positioning
  maxWidth: '400px', // Tablet-specific width
  paddingTop: `${sizes[8]}px`,
  padding: `${sizes[1]}px`,
  gap: `${sizes[4]}px`,
}));

const LeftView = styled.div(() => ({
  display: 'flex',
  alignItems: 'flex-start',
  gap: `${sizes[1]}px`,
  flexDirection: 'column',
}));

const RightView = styled.div(() => ({
  display: 'flex',
  alignItems: 'flex-start',
  gap: `${sizes[1]}px`,
  flexGrow: 1,
  maxWidth: '50%',
  flexDirection: 'column',
}));

const PageImageBackground = styled.div(() => ({
  flex: 1,
  width: '100%',
  height: '100vh',
  backgroundImage: `url(${reportBackground})`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  backgroundColor: '#fffbf6', // Tablet background color
  position: 'relative',
}));

const YourReportText = styled(H5)(() => ({
  color: colors.fwdDarkGreen[100],
  paddingBottom: `${sizes[6]}px`,
}));

const DifficultyTypeTitle = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
}));

const GreyDot = styled.div(() => ({
  width: '10px',
  height: '10px',
  backgroundColor: colors.fwdDarkGreen[20],
  borderRadius: '5px',
}));

const StarImage = styled.img(() => ({
  flex: 1,
  height: '30px',
  marginTop: `${sizes[4]}px`,
  objectFit: 'contain',
}));

const ScoreText = styled(Label)(() => ({
  textAlign: 'center',
  marginTop: `${sizes[2]}px`,
}));

const BackBtn = styled.button(() => ({
  paddingTop: `${sizes[5]}px`,
  paddingLeft: `${sizes[4]}px`,
  paddingRight: `${sizes[4]}px`,
  background: 'transparent',
  border: 'none',
  cursor: 'pointer',
}));

const LoadingContainer = styled.div(() => ({
  flex: 1,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
}));

const ScrollContainer = styled.div(() => ({
  margin: `${sizes[4]}px`,
  maxHeight: 'calc(100vh - 100px)',
  overflowY: 'auto',
  paddingBottom: '50px',
  display: 'flex',
  flexDirection: 'row', // Tablet layout: side-by-side
  gap: `${sizes[6]}px`,
}));

const LeftColumn = styled.div(() => ({
  flex: 1,
  maxWidth: '45%',
}));

const RightColumn = styled.div(() => ({
  flex: 1,
  maxWidth: '55%',
}));

const SafeAreaContainer = styled.div(() => ({
  paddingTop: '20px',
  height: '100%',
}));

const Spacer = styled.div<{ height: number }>((props) => ({
  height: `${props.height}px`,
}));

const SummaryCard = styled.div(() => ({
  backgroundColor: colors.white,
  borderRadius: '16px',
  border: `4px solid ${colors.fwdLightGreen[100]}`,
  boxShadow: '0 16px 32px rgba(0, 0, 0, 0.1)',
  padding: `0 ${sizes[4]}px ${sizes[4]}px`,
  marginTop: `${sizes[6]}px`,
}));

const OverallScoreContainer = styled.div(() => ({
  padding: `${sizes[12]}px`,
  marginTop: `${sizes[6]}px`,
  borderRadius: `${sizes[2]}px`,
  position: 'relative',
  minHeight: `${sizes[25]}px`,
  width: '260px', // Fixed width for tablet
  borderColor: 'white',
  backgroundColor: colors.fwdDarkGreen[100],
}));

const StarContainer = styled.div(() => ({
  marginBottom: `${sizes[4]}px`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'row',
}));

const HeartScore = styled.div(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'baseline',
  justifyContent: 'center',
}));

const CurrentScore = styled(H3)(() => ({
  textAlign: 'center',
  color: colors.white,
}));

const TotalScore = styled(H5)(() => ({
  textAlign: 'center',
  color: colors.white,
}));

const Button = styled.button<{ variant: 'primary' | 'secondary' }>((props) => ({
  flex: 1,
  padding: '12px 24px',
  borderRadius: '8px',
  border: props.variant === 'primary' ? 'none' : `2px solid ${colors.fwdDarkGreen[100]}`,
  backgroundColor: props.variant === 'primary' ? colors.fwdLightGreen[100] : 'transparent',
  color: props.variant === 'primary' ? colors.white : colors.fwdDarkGreen[100],
  fontWeight: 'bold',
  cursor: 'pointer',
  
  '&:hover': {
    opacity: 0.8,
  },
}));

// Simple ScoreContainer component
const SimpleScoreContainer = styled.div<{ noBorderTop?: boolean; noBorderBottom?: boolean }>((props) => ({
  border: '1px solid rgba(133, 157, 153, 0.50)',
  borderTopLeftRadius: props.noBorderTop ? '0' : '4px',
  borderTopRightRadius: props.noBorderTop ? '0' : '4px',
  borderBottomLeftRadius: props.noBorderBottom ? '0' : '4px',
  borderBottomRightRadius: props.noBorderBottom ? '0' : '4px',
  borderBottomColor: props.noBorderBottom ? 'transparent' : 'rgba(133, 157, 153, 0.50)',
  padding: '10px',
  marginBottom: '0',
  backgroundColor: colors.white,
  cursor: 'pointer',
}));

const StatusBar = styled.div<{ width: number; color: string }>((props) => ({
  borderRadius: '4px',
  backgroundColor: props.color,
  height: `${sizes[7]}px`,
  width: `${props.width}%`,
  flex: 1,
}));

interface ScoreContainerProps {
  title: string;
  score: number;
  skillSet?: any;
  color: string;
  noBorderTop?: boolean;
  noBorderBottom?: boolean;
}

const ScoreContainer: React.FC<ScoreContainerProps> = ({
  title,
  score,
  skillSet,
  color,
  noBorderTop,
  noBorderBottom,
}) => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const goDetailSummaryPage = () => {
    navigate(`/report/details?${searchParams.toString()}&skill=${encodeURIComponent(title)}`, {
      state: { skillSet }
    });
  };

  return (
    <SimpleScoreContainer
      noBorderTop={noBorderTop}
      noBorderBottom={noBorderBottom}
      onClick={goDetailSummaryPage}
    >
      <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginBottom: '8px' }}>
        <H8 style={{ color: colors.fwdDarkGreen[100], flex: 3 }}>{title}</H8>
        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', flex: 7 }}>
          <StatusBar width={score} color={score < 50 ? color : colors.fwdBlue[50]} />
          <H8 style={{ color: colors.fwdDarkGreen[100], marginLeft: '10px' }}>{score}</H8>
        </div>
      </div>
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div style={{ flex: 3 }}></div>
        <div style={{ flex: 7 }}>
          <SmallLabel style={{ color: colors.fwdOrange[100] }}>
            {score < 50 ? t('howYouCanDoBetter') : t('seeMoreDetails')}
          </SmallLabel>
        </div>
      </div>
    </SimpleScoreContainer>
  );
};

interface SummaryPageProps {}

const SummaryPageTablet: React.FC<SummaryPageProps> = () => {
  const { t } = useTranslation('ecoach');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  
  const conversationId = searchParams.get("conversationId") as string;
  
  // Use report from state if available
  const session = location.state?.report;
  
  const [summaryInfo, setSummaryInfo] = useState<any>(session);
  const [showFeedbackModal, setShowFeedbackModal] = useState<boolean>(!session);

  const { data, isLoading, error } = useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData({ conversationId }),
    refetchInterval: summaryInfo ? false : 5000,
    enabled: !session,
  });

  // Handle success case with useEffect (replaces deprecated onSuccess)
  React.useEffect(() => {
    if (data?.data?.report_is_ready === 'true' && !session) {
      setSummaryInfo(data.data);
    }
  }, [data, session]);

  // Handle error case with useEffect (replaces deprecated onError)
  React.useEffect(() => {
    if (error) {
      console.log('SummaryPage getConversationData error', JSON.stringify(error));
    }
  }, [error]);

  const reportData = session || summaryInfo;

  const getStarImage = (overallScore: number) => {
    if (overallScore < 14) return emptyStar;
    else if (overallScore >= 14 && overallScore < 28) return halfStar;
    else if (overallScore >= 28 && overallScore < 42) return oneStar;
    else if (overallScore >= 42 && overallScore < 56) return oneHalfStar;
    else if (overallScore >= 56 && overallScore < 70) return twoStar;
    else if (overallScore >= 70 && overallScore < 84) return twoHalfStar;
    else if (overallScore >= 84) return threeStar;
    return emptyStar;
  };

  const convertSecondsToMins = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = (duration % 60).toFixed(2);
    return `${minutes} ${t('min')} ${seconds} ${t('sec')}`;
  };

  const handleUserUpdate = () => {
    !showFeedbackModal && setShowFeedbackModal(true);
  };

  const goHistoryScreen = () => {
    navigate('/session-history');
  };

  const tryAgain = () => {
    const urlParams = new URLSearchParams(searchParams.toString());
    urlParams.delete("conversationId");
    urlParams.delete("ref");
    navigate(`/`);
  };

  const goBackBtn = () => {
    if (session) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  if (isLoading) {
    return (
      <LoadingContainer>
          <ReportLoading/>
      </LoadingContainer>
    );
  }

  if (!reportData) {
    return (
      <LoadingContainer>
          <ReportLoading/>
      </LoadingContainer>
    );
  }

  const { conversation_id, datetime, difficulty, duration, report } = reportData || {};

  const {
    overall_score,
    customer_relationship_score,
    skill_set_details,
    customer_discovery_score,
    applied_product_knowledge_score,
    objection_handling_closing_score,
    communication_skills_score,
  } = report || {};

  return (
    <PageImageBackground>
      <SafeAreaContainer>
        <BackBtn onClick={goBackBtn}>
          {session ? (
            <ArrowLeftIcon fill={colors.fwdDarkGreen[100]} />
          ) : (
            <CloseIcon fill={colors.fwdDarkGreen[100]} />
          )}
        </BackBtn>
        <ScrollContainer>
          {/* Left Column - Session Info */}
          <LeftColumn>
            <HorizontalView>
              <YourReportText fontWeight="bold">
                {t('yourReport')}
              </YourReportText>
            </HorizontalView>

            <HorizontalView>
              <LeftView>
                <HorizontalView>
                  <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[50]}>
                    {t('ssID')}
                  </SmallLabel>
                  <SmallLabel fontWeight="normal" color={colors.fwdDarkGreen[50]}>
                    {conversation_id}
                  </SmallLabel>
                </HorizontalView>

                <HorizontalView>
                  <DifficultyTypeTitle fontWeight="normal">
                    {t('difficulty')}
                  </DifficultyTypeTitle>
                  <H8 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
                    {difficulty === '1' ? t('beginner') : t('expert')}
                  </H8>
                </HorizontalView>
              </LeftView>
              <RightView>
                <HorizontalView>
                  <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[50]}>
                    {t('dateAndTime')}
                  </SmallLabel>
                  <SmallLabel fontWeight="normal" color={colors.fwdDarkGreen[50]}>
                    {datetime && dayjs(datetime).format('DD MMM YYYY')}
                  </SmallLabel>
                </HorizontalView>

                <HorizontalView>
                  <H8 fontWeight="normal" color={colors.fwdDarkGreen[100]}>
                    {t('duration')}
                  </H8>
                  <H8 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
                    {duration && convertSecondsToMins(duration)}
                  </H8>
                </HorizontalView>
              </RightView>
            </HorizontalView>

            {/* Overall Score Card - Tablet specific styling */}
            {overall_score && (
              <OverallScoreContainer>
                <StarContainer>
                  <StarImage
                    src={getStarImage(extractStringScore(overall_score))}
                    alt="star rating"
                    style={{ width: '60px', height: '60px' }} // Larger for tablet
                  />
                </StarContainer>
                <ScoreText fontWeight="bold" color={colors.white}>
                  {t('overallScore')}
                </ScoreText>
                <HeartScore>
                  <CurrentScore fontWeight="bold">
                    {extractStringScore(overall_score)}
                  </CurrentScore>
                  <TotalScore fontWeight="bold">
                    / 100
                  </TotalScore>
                </HeartScore>
              </OverallScoreContainer>
            )}
          </LeftColumn>

          {/* Right Column - Skill Breakdown */}
          <RightColumn>
            {overall_score && skill_set_details && (
              <SummaryCard>
                <Spacer height={sizes[4]} />
                {customer_relationship_score && (
                  <ScoreContainer
                    title={t('relationshipBuilding')}
                    score={extractStringScore(customer_relationship_score)}
                    skillSet={skill_set_details?.customer_relationship}
                    color={colors.fwdLightGreen[100]}
                    noBorderBottom={
                      !!customer_discovery_score ||
                      !!applied_product_knowledge_score ||
                      !!objection_handling_closing_score ||
                      !!communication_skills_score
                    }
                  />
                )}

                {customer_discovery_score && (
                  <ScoreContainer
                    title={t('customerDiscovery')}
                    score={extractStringScore(customer_discovery_score)}
                    skillSet={skill_set_details?.customer_discovery}
                    color={colors.fwdOrange[100]}
                    noBorderTop={!!customer_relationship_score}
                    noBorderBottom={
                      !!applied_product_knowledge_score ||
                      !!objection_handling_closing_score ||
                      !!communication_skills_score
                    }
                  />
                )}

                {applied_product_knowledge_score && (
                  <ScoreContainer
                    title={t('productKnowledge')}
                    score={extractStringScore(applied_product_knowledge_score)}
                    skillSet={skill_set_details?.applied_product_knowledge}
                    color={colors.fwdOrange[100]}
                    noBorderTop={
                      !!customer_discovery_score ||
                      !!customer_relationship_score
                    }
                    noBorderBottom={
                      !!objection_handling_closing_score ||
                      !!communication_skills_score
                    }
                  />
                )}

                {objection_handling_closing_score && (
                  <ScoreContainer
                    title={t('objectionsAndClosing')}
                    score={extractStringScore(objection_handling_closing_score)}
                    skillSet={skill_set_details?.objection_handling_closing}
                    color={colors.fwdOrange[100]}
                    noBorderTop={
                      !!customer_discovery_score ||
                      !!applied_product_knowledge_score ||
                      !!customer_relationship_score
                    }
                    noBorderBottom={!!communication_skills_score}
                  />
                )}

                {communication_skills_score && (
                  <ScoreContainer
                    title={t('speechAndAnalysis')}
                    score={extractStringScore(communication_skills_score)}
                    skillSet={skill_set_details?.communication_skills}
                    color={colors.fwdOrange[100]}
                    noBorderTop={
                      !!customer_discovery_score ||
                      !!applied_product_knowledge_score ||
                      !!objection_handling_closing_score ||
                      !!customer_relationship_score
                    }
                  />
                )}

                <Spacer height={sizes[3]} />
                <HorizontalView>
                  <GreyDot />
                  <GreyDot />
                </HorizontalView>
              </SummaryCard>
            )}

            <Spacer height={sizes[4]} />
            {!session && (
              <BtnGroupView>
                <Button
                  variant="primary"
                  onClick={tryAgain}
                >
                  {t('letsTryAgain')}
                </Button>
                <Button
                  variant="secondary"
                  onClick={goHistoryScreen}
                >
                  {t('viewHistory')}
                </Button>
              </BtnGroupView>
            )}
          </RightColumn>
        </ScrollContainer>
      </SafeAreaContainer>
    </PageImageBackground>
  );
};

export default SummaryPageTablet; 