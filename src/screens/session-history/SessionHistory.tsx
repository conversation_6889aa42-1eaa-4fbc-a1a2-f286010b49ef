import { FC, useEffect, useMemo, useRef, useState } from "react";
import useWindowResize from "@hooks/use-window-resize";
import styled from "styled-components";
import { useNavigate, useSearchParams } from "react-router-dom";
import { colors, H6, sizes } from "../../components/CubeBaseElement";
import LoadingSection from "@components/loading";
import { ConversationData, ConversationType } from "../../@custom-types/ecoach";
import { device } from "@styles/media";
import { useReportHistoryLatest } from "../../hooks/useReportHistoryLatest";
import { useTranslation } from "react-i18next";
import { Tabs, TabItem } from "@/components/tabs";
import SessionQuickFireCard from "@/components/cards/SessionQuickFireCard";
import { orderBy, uniqBy } from "lodash";
import {
  BackButton,
  BackgroundImageOverlay,
  DarkBackgroundOverlay,
  ScreenContainer,
  ScreenContent,
  ScreenHeader,
} from "@/components/ScreenContainer";

const Content = styled.div`
  display: flex;
  gap: ${sizes[0]}px;
  flex-direction: column;
  @media ${device.noMobile} {
    justify-content: space-between;
    flex-direction: row;
    margin-bottom: ${sizes[4]}px;
  }
`;

const TABS = [
  {
    label: "Roleplay simulasi",
    conversationType: ConversationType.FULL_EXPERIENCE,
  },
  { label: "Pengetahuan produk", conversationType: ConversationType.QUICKFIRE },
  {
    label: "Penanganan objeksi",
    conversationType: ConversationType.OBJECTION_HANDLING,
  },
];

const SessionHistory = () => {
  const { t } = useTranslation("ecoach");
  const { width } = useWindowResize();
  const noMobile = width > 768;
  const navigate = useNavigate();
  const { data: sessionList, loading } = useReportHistoryLatest(20);
  // TODO: Add hasMore and fetchNextPage to the hook
  const hasMore = false;
  const fetchNextPage = () => {};
  const containerRef = useRef<HTMLDivElement>(null);

  const handleScroll = () => {
    if (!containerRef.current || loading || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    if (scrollTop + clientHeight >= scrollHeight - 50) {
      fetchNextPage && sessionList && fetchNextPage();
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    container.addEventListener("scroll", handleScroll);
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [sessionList, loading, hasMore]);

  return (
    <div>
      <DarkBackgroundOverlay />
      <BackgroundImageOverlay />
      <ScreenContainer ref={containerRef}>
        <ScreenHeader>
          <BackButton onClick={() => navigate(-1)} />
          <H6 fontWeight="bold" color={colors.white}>
            {t("yourPastSS")}
          </H6>
        </ScreenHeader>
        <ScreenContent
          style={{
            display: "flex",
            flexDirection: "column",
            flex: 1,
            overflowY: "auto",
          }}
        >
          <RecentSessions />
        </ScreenContent>
      </ScreenContainer>
    </div>
  );
};

export default SessionHistory;

export type RecentSessionsProps = {};

export const RecentSessions: FC<RecentSessionsProps> = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const selectedConversationType =
    searchParams.get("conversationType") || ConversationType.FULL_EXPERIENCE;
  const [activeTabIndex, setActiveTabIndex] = useState(
    TABS.findIndex((tab) => tab.conversationType === selectedConversationType)
  );

  const handleTabChange = (index: number) => {
    setActiveTabIndex(index);
    const conversationType = TABS[index].conversationType;
    navigate(`/session-history?conversationType=${conversationType}`, {
      replace: true,
      preventScrollReset: true,
    });
  };

  return (
    <>
      <Tabs activeIndex={activeTabIndex} onChange={handleTabChange}>
        {TABS.map((tab, index) => (
          <TabItem key={index}>{tab.label}</TabItem>
        ))}
      </Tabs>
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          overflow: "hidden",
          flex: 1,
        }}
      >
        {TABS.map((tab, index) => (
          <SessionList
            key={index}
            isActive={activeTabIndex === index}
            conversationType={tab.conversationType}
          />
        ))}
      </div>
    </>
  );
};

export type SessionListProps = {
  conversationType: ConversationType;
  isActive?: boolean;
};

export const SessionList: FC<SessionListProps> = ({
  conversationType,
  isActive,
}) => {
  const { data, loading } = useReportHistoryLatest(20, conversationType);
  const sessionList = useMemo(() => {
    if (!data) {
      return [];
    }
    return data;
  }, [data]);
  const rows: ConversationData[] = orderBy(
    uniqBy(sessionList, "conversation_id"),
    "datetime",
    "desc"
  );

  // Refactored: Only check isActive once
  const activeStyles = isActive
    ? {
        flex: 1,
        margin: `0 -${sizes[4]}px`,
        padding: `0 ${sizes[4]}px`,
        width: "100%",
        overflow: "auto",
        paddingTop: `${sizes[4]}px`,
      }
    : {
        flex: 0,
        margin: 0,
        padding: 0,
        width: "0",
        overflow: "hidden",
        paddingTop: 0,
      };

  return (
    <div style={{ display: "flex", flexDirection: "column", ...activeStyles }}>
      {loading && rows.length === 0 && <LoadingSection loading={true} />}
      {rows.map((item) => (
        <Content key={`row-${item.conversation_id}`}>
          <SessionQuickFireCard
            session={item}
            sessionNumber={item.session_number || ""}
            key={item.conversation_id}
          />
        </Content>
      ))}
    </div>
  );
};
