import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  ConversationData,
  DEFAULT_QUICKFIRE_COMPLETED_HEARTS,
  EcoachConfigurationData,
  EcoachState,
} from "../types/ecoach";

const initialState: EcoachState = {
  isQuickfire: true,
  isTrainerGuruEnabled: false,
  quickfireVideoUrl: null,
  quickfireCompletedHearts: DEFAULT_QUICKFIRE_COMPLETED_HEARTS,
  cubeToken: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  conversationsData: {},
};

const ecoachSlice = createSlice({
  name: "ecoach",
  initialState: initialState,
  reducers: {
    resetState: () => {
      return initialState;
    },

    setToken: (state, action: PayloadAction<string>) => {
      state.cubeToken = action.payload;
    },

    setConversationData: (state, action: PayloadAction<ConversationData>) => {
      state.conversationsData[action.payload.conversation_id] = action.payload;
    },

    setupConfiguration: (state, action: PayloadAction<EcoachConfigurationData>) => {
      const {
        isQuickfire,
        isTrainerGuruEnabled,
        quickfireVideoUrl,
        homepageBackground,
        quickfireCompletedHearts,
        productConfig,
        moduleAvailability,
        quickfireProductConfig,
        objectionHandlingProductConfig,
      } = action.payload;

      state.isQuickfire = isQuickfire;
      state.isTrainerGuruEnabled = isTrainerGuruEnabled;
      state.quickfireVideoUrl = quickfireVideoUrl;
      state.homepageBackground = homepageBackground;
      state.quickfireCompletedHearts = quickfireCompletedHearts;
      state.productConfig = productConfig;
      state.moduleAvailability = moduleAvailability;
      state.quickfireProductConfig = quickfireProductConfig;
      state.objectionHandlingProductConfig = objectionHandlingProductConfig;
    },
  },
});

export const { setupConfiguration, setToken, setConversationData, resetState } = ecoachSlice.actions;

export default ecoachSlice.reducer;
