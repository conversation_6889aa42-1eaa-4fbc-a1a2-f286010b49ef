<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Redirecting...</title>
    <script>
      // If not already at index.html, redirect to it and preserve the path
      if (location.pathname !== '/index.html') {
        var newUrl = '/index.html' + location.search + location.hash;
        window.location.replace(newUrl);
      }
    </script>
  </head>
  <body>
    <h1>Redirecting...</h1>
  </body>
</html> 